require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::Vehicles", type: :request do
  include_context "dealership_api_shared_context"

  path '/api/v1/dealerships/{dealership_uuid}/vehicles' do
    parameter name: 'dealership_uuid', in: :path, type: :string, description: 'Dealership UUID'

    get 'Retrieves dealership vehicles' do
      tags 'Vehicles'
      description 'Retrieves a paginated list of vehicles for a specific dealership'
      consumes 'application/json'
      produces 'application/json'
      security [ Bearer: [] ]

      parameter name: 'Authorization', in: :header, type: :string, required: true,
                description: 'Bearer token in the format: Bearer <token>'

      parameter name: 'Device-ID', in: :header, type: :string, required: true,
                description: 'Device ID for authentication'

      parameter name: 'page', in: :query, type: :integer, required: false,
                description: 'Page number for pagination (default: 1)'

      parameter name: 'per_page', in: :query, type: :integer, required: false,
                description: 'Number of items per page (default: 20, max: 100)'

      parameter name: 'vehicle_type', in: :query, type: :string, required: false,
                description: 'Filter by vehicle type',
                enum: [ 'new_vehicle', 'demo', 'old' ]

      parameter name: 'query', in: :query, type: :string, required: false,
                description: 'Search vehicles by stock_number, rego, make, model, color, or build_year (minimum 3 characters)'

      response '200', 'Vehicles retrieved successfully' do
        let(:dealership_uuid) { dealership.uuid }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: 'Dealership vehicles retrieved successfully' }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                     vehicles: {
                       type: :array,
                       items: {
                         type: :object,
                         properties: {
                           uuid: { type: :string, format: :uuid },
                           make: { type: :string, example: 'Toyota' },
                           model: { type: :string, example: 'Camry' },
                           build_year: { type: :integer, example: 2023 },
                           rego: { type: :string, example: 'ABC123' },
                           vin: { type: :string, example: '12345678901234567' },
                           stock_number: { type: :string, example: 'T001' },
                           color: { type: :string, example: 'Red' },
                           vehicle_type: { type: :string, example: 'new_vehicle' },
                           status: { type: :string, example: 'available' },
                           last_known_odometer_km: { type: :integer, example: 10000 },
                           last_known_fuel_gauge_level: { type: :integer, example: 75 },
                           display_name: { type: :string, example: '2023 Toyota Camry' },
                           last_system_inspection_timestamp: { type: :string, format: :datetime },
                           rego_expiry: { type: :string, format: :date },
                           is_trade_plate_used: { type: :boolean, example: false },
                           available_for_drive: { type: :boolean, example: true },
                           created_at: { type: :string, format: :datetime },
                           updated_at: { type: :string, format: :datetime }
                         }
                       }
                     }
                   }
                 }
               }

        header 'X-Current-Page', schema: { type: :string }, description: 'Current page number'
        header 'X-Per-Page', schema: { type: :string }, description: 'Items per page'
        header 'X-Total-Count', schema: { type: :string }, description: 'Total number of items'
        header 'X-Total-Pages', schema: { type: :string }, description: 'Total number of pages'

        run_test! do |response|
          data = JSON.parse(response.body)
          expect(data.dig("status", "message")).to eq("Dealership vehicles retrieved successfully")
          expect(data.dig("data", "vehicles")).to be_an(Array)

          # Check pagination headers
          expect(response.headers["X-Current-Page"]).to be_present
          expect(response.headers["X-Per-Page"]).to be_present
          expect(response.headers["X-Total-Count"]).to be_present
          expect(response.headers["X-Total-Pages"]).to be_present
        end
      end

      response '200', 'Vehicles filtered by type' do
        let(:dealership_uuid) { dealership.uuid }
        let(:vehicle_type) { 'new_vehicle' }
        let!(:new_vehicle) { create(:vehicle, dealership: dealership, vehicle_type: :new_vehicle) }
        let!(:demo_vehicle) { create(:vehicle, dealership: dealership, vehicle_type: :demo) }

        run_test! do |response|
          data = JSON.parse(response.body)
          vehicles = data.dig("data", "vehicles")
          expect(vehicles.all? { |v| v["vehicle_type"] == "new_vehicle" }).to be true
        end
      end

      response '422', 'Invalid vehicle type filter' do
        let(:dealership_uuid) { dealership.uuid }
        let(:vehicle_type) { 'invalid_type' }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: 'Invalid vehicle type filter. Valid filters: new_vehicle, demo, old' }
                   }
                 }
               }

        run_test!
      end

      response '401', 'Unauthorized' do
        let(:dealership_uuid) { dealership.uuid }
        let(:Authorization) { 'Bearer invalid_token' }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Invalid or expired token' }
                   }
                 }
               }

        run_test!
      end

      response '404', 'Dealership not found' do
        let(:dealership_uuid) { 'non-existent-uuid' }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: 'Dealership not found' }
                   }
                 }
               }

        run_test!
      end

      response '401', 'Missing Device-ID header' do
        let(:dealership_uuid) { dealership.uuid }
        let(:"Device-ID") { nil }

        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: 'Invalid device' }
                   }
                 }
               }

        run_test!
      end
    end
  end

  # Non-Swagger tests for additional coverage
  describe "GET /api/v1/dealerships/:dealership_uuid/vehicles" do
    subject { get "/api/v1/dealerships/#{dealership.uuid}/vehicles", params: params, headers: headers }

    let(:params) { {} }

    context "with vehicles in the dealership" do
      let!(:vehicle1) { create(:vehicle, dealership: dealership, make: 'Toyota', model: 'Camry', build_year: 2021, stock_number: 'T001', rego: 'ABC123', color: 'Red', vehicle_type: :new_vehicle, status: :available) }
      let!(:vehicle2) { create(:vehicle, dealership: dealership, make: 'Honda', model: 'Civic', build_year: 2022, stock_number: 'H002', rego: 'DEF456', color: 'Blue', vehicle_type: :demo, status: :in_use) }
      let!(:search_vehicle) { create(:vehicle, dealership: dealership, make: 'Mazda', model: 'CX-5', stock_number: 'SEARCH001', rego: 'SEARCH123', color: 'UniqueTestColor', build_year: 2023, vehicle_type: :old, status: :enquiry) }
      let!(:deleted_vehicle) { create(:vehicle, dealership: dealership, status: :deleted) }

      it "returns vehicles ordered by name and excludes deleted vehicles" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        # Rails.logger.info "JSON: #{json}"
        vehicles = json.dig("data", "vehicles")
        expect(vehicles.size).to eq(3)
        expect(vehicles.map { |v| v["uuid"] }).not_to include(deleted_vehicle.uuid)
      end

      context "with pagination parameters" do
        let(:params) { { page: 1, per_page: 1 } }

        it "respects pagination parameters" do
          subject
          expect(response).to have_http_status(:ok)

          expect(response.headers["X-Current-Page"]).to eq("1")
          expect(response.headers["X-Per-Page"]).to eq("1")
          expect(response.headers["X-Total-Count"]).to eq("3")
          expect(response.headers["X-Total-Pages"]).to eq("3")
          expect(response.headers["X-Next-Page"]).to eq("2")
          expect(response.headers["X-Prev-Page"]).to be_nil

          json = response.parsed_body
          vehicles = json.dig("data", "vehicles")
          expect(vehicles.size).to eq(1)
        end
      end

      context "with vehicle_type filter" do
        let(:params) { { vehicle_type: 'demo' } }

        it "filters vehicles by type" do
          subject
          expect(response).to have_http_status(:ok)
          json = response.parsed_body

          vehicles = json.dig("data", "vehicles")
          expect(vehicles.size).to eq(1)
          expect(vehicles.first["vehicle_type"]).to eq("demo")
          expect(vehicles.first["uuid"]).to eq(vehicle2.uuid)
        end
      end

      context "with query parameter" do
        context "when searching by stock_number" do
          let(:params) { { query: 'SEAR' } }

          it "returns vehicles matching the stock number" do
            subject
            expect(response).to have_http_status(:ok)
            json = response.parsed_body

            vehicles = json.dig("data", "vehicles")
            expect(vehicles.size).to eq(1)
            expect(vehicles.first["uuid"]).to eq(search_vehicle.uuid)
            expect(vehicles.first["stock_number"]).to eq("SEARCH001")
          end
        end

        context "when searching by rego" do
          let(:params) { { query: 'ARCH1' } }

          it "returns vehicles matching the rego" do
            subject
            expect(response).to have_http_status(:ok)
            json = response.parsed_body

            vehicles = json.dig("data", "vehicles")
            expect(vehicles.size).to eq(1)
            expect(vehicles.first["uuid"]).to eq(search_vehicle.uuid)
            expect(vehicles.first["rego"]).to eq("SEARCH123")
          end
        end

        context "when searching by make" do
          let(:params) { { query: 'Maz' } }

          it "returns vehicles matching the make" do
            subject
            expect(response).to have_http_status(:ok)
            json = response.parsed_body

            vehicles = json.dig("data", "vehicles")
            vehicle_uuids = vehicles.map { |v| v["uuid"] }
            expect(vehicle_uuids).to include(search_vehicle.uuid)
            expect(vehicles.find { |v| v["uuid"] == search_vehicle.uuid }["make"]).to eq("Mazda")
          end
        end

        context "when searching by model" do
          let(:params) { { query: 'CX-5' } }

          it "returns vehicles matching the model" do
            subject
            expect(response).to have_http_status(:ok)
            json = response.parsed_body

            vehicles = json.dig("data", "vehicles")
            vehicle_uuids = vehicles.map { |v| v["uuid"] }
            expect(vehicle_uuids).to include(search_vehicle.uuid)
            expect(vehicles.find { |v| v["uuid"] == search_vehicle.uuid }["model"]).to eq("CX-5")
          end
        end

        context "when searching by color" do
          let(:params) { { query: 'Uniq' } }

          it "returns vehicles matching the color" do
            subject
            expect(response).to have_http_status(:ok)
            json = response.parsed_body

            vehicles = json.dig("data", "vehicles")
            vehicle_uuids = vehicles.map { |v| v["uuid"] }
            expect(vehicle_uuids).to include(search_vehicle.uuid)
            expect(vehicles.find { |v| v["uuid"] == search_vehicle.uuid }["color"]).to eq("UniqueTestColor")
          end
        end

        context "when searching by build_year" do
          let(:params) { { query: '023' } }

          it "returns vehicles matching the build year" do
            subject
            expect(response).to have_http_status(:ok)
            json = response.parsed_body

            vehicles = json.dig("data", "vehicles")
            vehicle_uuids = vehicles.map { |v| v["uuid"] }
            expect(vehicle_uuids).to include(search_vehicle.uuid)
            expect(vehicles.find { |v| v["uuid"] == search_vehicle.uuid }["build_year"]).to eq(2023)
          end
        end

        context "when search term is too short" do
          let(:params) { { query: 'To' } }

          it "returns empty results" do
            subject
            expect(response).to have_http_status(:ok)
            json = response.parsed_body

            vehicles = json.dig("data", "vehicles")
            expect(vehicles).to be_empty
          end
        end

        context "when search term is too long" do
          let(:params) { { query: 'a' * 21 } }

          it "returns empty results" do
            subject
            expect(response).to have_http_status(:ok)
            json = response.parsed_body

            vehicles = json.dig("data", "vehicles")
            expect(vehicles).to be_empty
          end
        end

        context "when search term is case insensitive" do
          let(:params) { { query: 'maz' } }

          it "returns vehicles matching case insensitively" do
            subject
            expect(response).to have_http_status(:ok)
            json = response.parsed_body

            vehicles = json.dig("data", "vehicles")
            vehicle_uuids = vehicles.map { |v| v["uuid"] }
            expect(vehicle_uuids).to include(search_vehicle.uuid)
          end
        end

        context "when no vehicles match search term" do
          let(:params) { { query: 'NonExistent' } }

          it "returns empty results" do
            subject
            expect(response).to have_http_status(:ok)
            json = response.parsed_body

            vehicles = json.dig("data", "vehicles")
            expect(vehicles).to be_empty
          end
        end
      end

      context "with invalid vehicle_type filter" do
        let(:params) { { vehicle_type: 'invalid_type' } }

        it "returns error for invalid vehicle type" do
          subject
          expect(response).to have_http_status(:unprocessable_entity)
          json = response.parsed_body
          expect(json.dig("status", "message")).to include("Invalid vehicle type filter")
        end
      end
    end

    context "when dealership has no vehicles" do
      it "returns empty array" do
        subject
        expect(response).to have_http_status(:ok)
        json = response.parsed_body

        expect(json.dig("status", "message")).to eq("Dealership vehicles retrieved successfully")
        expect(json.dig("data", "vehicles")).to eq([])
      end
    end

    context "when user doesn't have access to dealership" do
      let(:other_dealership) { create(:dealership) }

      it "returns not found" do
        get "/api/v1/dealerships/#{other_dealership.uuid}/vehicles", headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with invalid dealership UUID" do
      it "returns not found" do
        get "/api/v1/dealerships/invalid-uuid/vehicles", headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end

    context "with invalid per_page parameter" do
      let(:params) { { per_page: 150 } }

      it "limits per_page to maximum allowed" do
        subject
        expect(response).to have_http_status(:ok)
        expect(response.headers["X-Per-Page"]).to eq("100")
      end
    end
  end
end
