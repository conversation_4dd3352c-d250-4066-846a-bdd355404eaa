require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"
  let!(:vehicle1) { create(:vehicle, dealership: dealership) }
  let!(:customer1) { create(:customer, dealership: dealership) }

  path "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/damage-report" do
    post "Create damage report for drive" do
      tags "Drives"
      consumes "multipart/form-data"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :uuid, in: :path, type: :string, required: true, description: "Drive UUID"
      parameter name: :damage_type, in: :formData, type: :string, required: true, description: "Type of damage report", enum: [ "initial", "final" ]
      parameter name: :damage_notes, in: :formData, type: :string, required: true, description: "Damage report notes"
      parameter name: :media_files, in: :formData, type: :file, required: false, description: "Media files (photos/videos)"

      response "201", "Initial damage report created successfully" do
        let(:test_drive) { create(:drive, dealership: dealership, drive_type: :test_drive, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:damage_type) { "initial" }
        let(:damage_notes) { "Minor scratches on the front bumper" }
        let(:media_file1) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png") }
        let(:media_file2) { fixture_file_upload(Rails.root.join("spec/fixtures/files/car_2.jpg"), "image/jpeg") }
        let(:media_files) { [ media_file1, media_file2 ] }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Initial damage report created successfully")
          expect(json.dig("data", "damage_report")).to be_present
          expect(json.dig("data", "damage_report", "report_type")).to eq("initial")
          expect(json.dig("data", "damage_report", "description")).to eq("Minor scratches on the front bumper")
          expect(json.dig("data", "damage_report", "media_files_count")).to eq(2)
          expect(json.dig("data", "damage_report", "media_files")).to be_an(Array)
          expect(json.dig("data", "damage_report", "media_files").length).to eq(2)
          expect(json.dig("data", "drive")).to be_present
        end
      end

      response "201", "Final damage report created successfully" do
        let(:test_drive) { create(:drive, dealership: dealership, drive_type: :loan, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:damage_type) { "final" }
        let(:damage_notes) { "No additional damage found" }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Final damage report created successfully")
          expect(json.dig("data", "damage_report")).to be_present
          expect(json.dig("data", "damage_report", "report_type")).to eq("final")
          expect(json.dig("data", "damage_report", "description")).to eq("No additional damage found")
          expect(json.dig("data", "damage_report", "media_files_count")).to eq(0)
          expect(json.dig("data", "drive")).to be_present
        end
      end

      response "201", "Damage report created without media files" do
        let(:test_drive) { create(:drive, dealership: dealership, drive_type: :self_loan, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:damage_type) { "initial" }
        let(:damage_notes) { "Vehicle in perfect condition" }

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Initial damage report created successfully")
          expect(json.dig("data", "damage_report")).to be_present
          expect(json.dig("data", "damage_report", "media_files_count")).to eq(0)
          expect(json.dig("data", "damage_report", "media_files")).to eq([])
          expect(json.dig("data", "drive")).to be_present
        end
      end

      response "422", "Validation error" do
        let(:test_drive) { create(:drive, dealership: dealership, drive_type: :test_drive, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }

        context "when damage type is missing" do
          let(:damage_type) { nil }
          let(:damage_notes) { "Some damage notes" }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to eq("Damage type is required")
          end
        end

        context "when damage type is invalid" do
          let(:damage_type) { "invalid_type" }
          let(:damage_notes) { "Some damage notes" }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to eq("Invalid damage type. Must be one of: initial, final, vehicle")
          end
        end

        context "when damage notes are missing" do
          let(:damage_type) { "initial" }
          let(:damage_notes) { nil }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to eq("Damage notes are required")
          end
        end

        context "when damage report already exists" do
          let(:damage_type) { "initial" }
          let(:damage_notes) { "Some damage notes" }

          before do
            create(:damage_report, drive: test_drive, vehicle: test_drive.vehicle, report_type: :initial)
          end

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to eq("Initial damage report already exists for this drive")
          end
        end
      end

      response "404", "Drive not found" do
        let(:uuid) { "non-existent-uuid" }
        let(:damage_type) { "initial" }
        let(:damage_notes) { "Some damage notes" }

        run_test!
      end
    end
  end

  # Regular RSpec request specs
  describe "POST /api/v1/dealerships/:dealership_uuid/drives/:uuid/damage-report" do
    let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle, customer: customer, sales_person: sales_person, drive_type: :test_drive) }
    let(:url) { "/api/v1/dealerships/#{dealership.uuid}/drives/#{test_drive.uuid}/damage-report" }

    context "with valid parameters" do
      it "creates initial damage report successfully" do
        media_file1 = fixture_file_upload(Rails.root.join("spec/fixtures/files/car_1.png"), "image/png")
        media_file2 = fixture_file_upload(Rails.root.join("spec/fixtures/files/car_2.jpg"), "image/jpeg")

        params = {
          damage_type: "initial",
          damage_notes: "Minor scratches on the front bumper",
          media_files: [ media_file1, media_file2 ]
        }

        post url, params: params, headers: headers
        expect(response).to have_http_status(:created)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Initial damage report created successfully")
        expect(json.dig("data", "damage_report")).to be_present
        expect(json.dig("data", "damage_report", "report_type")).to eq("initial")
        expect(json.dig("data", "damage_report", "description")).to eq("Minor scratches on the front bumper")
        expect(json.dig("data", "damage_report", "media_files_count")).to eq(2)
        expect(json.dig("data", "drive")).to be_present
      end

      it "creates final damage report successfully" do
        params = {
          damage_type: "final",
          damage_notes: "No additional damage found"
        }

        post url, params: params, headers: headers
        expect(response).to have_http_status(:created)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Final damage report created successfully")
        expect(json.dig("data", "damage_report")).to be_present
        expect(json.dig("data", "damage_report", "report_type")).to eq("final")
        expect(json.dig("data", "damage_report", "description")).to eq("No additional damage found")
        expect(json.dig("data", "damage_report", "media_files_count")).to eq(0)
        expect(json.dig("data", "drive")).to be_present
      end

      it "creates damage report without media files" do
        params = {
          damage_type: "initial",
          damage_notes: "Vehicle in perfect condition"
        }

        post url, params: params, headers: headers
        expect(response).to have_http_status(:created)

        json = response.parsed_body
        expect(json.dig("status", "message")).to eq("Initial damage report created successfully")
        expect(json.dig("data", "damage_report")).to be_present
        expect(json.dig("data", "damage_report", "report_type")).to eq("initial")
        expect(json.dig("data", "damage_report", "description")).to eq("Vehicle in perfect condition")
        expect(json.dig("data", "damage_report", "media_files_count")).to eq(0)
        expect(json.dig("data", "drive")).to be_present
      end
    end

    context "validation errors" do
      it "returns error when damage type is missing" do
        params = {
          damage_notes: "Some damage notes"
        }

        post url, params: params, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Damage type is required")
      end

      it "returns error when damage type is invalid" do
        params = {
          damage_type: "invalid_type",
          damage_notes: "Some damage notes"
        }

        post url, params: params, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Invalid damage type. Must be one of: initial, final, vehicle")
      end

      it "returns error when damage notes are missing" do
        params = {
          damage_type: "initial"
        }

        post url, params: params, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Damage notes are required")
      end

      it "returns error when damage report already exists" do
        # Create an existing damage report
        create(:damage_report, drive: test_drive, vehicle: test_drive.vehicle, report_type: :initial)

        params = {
          damage_type: "initial",
          damage_notes: "Some damage notes"
        }

        post url, params: params, headers: headers
        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Initial damage report already exists for this drive")
      end
    end

    context "drive not found" do
      it "returns 404 when drive doesn't exist" do
        url = "/api/v1/dealerships/#{dealership.uuid}/drives/non-existent-uuid/damage-report"
        params = {
          damage_type: "initial",
          damage_notes: "Some damage notes"
        }

        post url, params: params, headers: headers
        expect(response).to have_http_status(:not_found)
      end
    end
  end
end
