# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  let!(:vehicle_with_blank_rego) { create(:vehicle, dealership: dealership, rego: nil) }
  let!(:vehicle_with_rego) { create(:vehicle, dealership: dealership, rego: "ABC123") }
  let!(:active_trade_plate) { create(:trade_plate, dealership: dealership, status: :active, expiry: 1.month.from_now) }
  let!(:inactive_trade_plate) { create(:trade_plate, dealership: dealership, status: :inactive) }
  let!(:expired_trade_plate) { create(:trade_plate, dealership: dealership, status: :active, expiry: 1.day.ago) }

  let!(:drive_with_blank_rego) do
    create(:drive,
           dealership: dealership,
           vehicle: vehicle_with_blank_rego,
           sales_person: sales_person,
           drive_type: :test_drive,
           status: :draft)
  end

  let!(:drive_with_rego) do
    create(:drive,
           dealership: dealership,
           vehicle: vehicle_with_rego,
           sales_person: sales_person,
           drive_type: :test_drive,
           status: :scheduled)
  end

  let!(:drive_with_trade_plate_in_use) do
    create(:drive,
           dealership: dealership,
           vehicle: create(:vehicle, dealership: dealership, rego: nil),
           sales_person: sales_person,
           drive_type: :test_drive,
           status: :in_progress,
           trade_plate: active_trade_plate)
  end

  describe "PATCH /api/v1/dealerships/:dealership_uuid/drives/:uuid/trade-plate" do
    let(:url) { "/api/v1/dealerships/#{dealership_uuid}/drives/#{drive_uuid}/trade-plate" }

    context "when assigning trade plate successfully" do
      let(:drive_uuid) { drive_with_blank_rego.uuid }
      let(:params) { { trade_plate_uuid: active_trade_plate.uuid } }

      it "assigns the trade plate to the drive" do
        patch url, params: params, headers: headers

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response["status"]["message"]).to eq("Trade plate assigned successfully")
        expect(json_response["data"]["drive"]["trade_plate"]["uuid"]).to eq(active_trade_plate.uuid)
        expect(json_response["data"]["drive"]["trade_plate"]["number"]).to eq(active_trade_plate.number)

        # Verify the drive was updated in the database
        drive_with_blank_rego.reload
        expect(drive_with_blank_rego.trade_plate).to eq(active_trade_plate)
      end
    end

    context "when trade_plate_uuid is missing" do
      let(:drive_uuid) { drive_with_blank_rego.uuid }
      let(:params) { {} }

      it "returns an error" do
        patch url, params: params, headers: headers

        expect(response).to have_http_status(:unprocessable_entity)

        json_response = response.parsed_body
        expect(json_response["status"]["message"]).to eq("Trade plate UUID is required")
      end
    end

    context "when vehicle has a registration number" do
      let(:drive_uuid) { drive_with_rego.uuid }
      let(:params) { { trade_plate_uuid: active_trade_plate.uuid } }

      it "returns an error" do
        patch url, params: params, headers: headers

        expect(response).to have_http_status(:unprocessable_entity)

        json_response = response.parsed_body
        expect(json_response["status"]["message"]).to include("Trade plate can only be assigned to vehicles with blank registration")
      end
    end

    context "when drive is not a vehicle out drive" do
      let!(:drive_with_booking) do
        create(:drive,
              dealership: dealership,
              vehicle: vehicle_with_blank_rego,
              sales_person: sales_person,
              drive_type: :test_drive_booking,
              status: :scheduled)
      end

      let(:drive_uuid) { drive_with_booking.uuid }
      let(:params) { { trade_plate_uuid: active_trade_plate.uuid } }

      it "returns an error" do
        patch url, params: params, headers: headers

        expect(response).to have_http_status(:unprocessable_entity)

        json_response = response.parsed_body
        expect(json_response["status"]["message"]).to include("Trade plate can only be assigned to vehicle out drives")
      end
    end

    context "when trade plate does not exist" do
      let(:drive_uuid) { drive_with_blank_rego.uuid }
      let(:params) { { trade_plate_uuid: "non-existent-uuid" } }

      it "returns an error" do
        patch url, params: params, headers: headers

        expect(response).to have_http_status(:not_found)

        json_response = response.parsed_body
        expect(json_response["status"]["message"]).to eq("Trade plate not found or does not belong to this dealership")
      end
    end

    context "when trade plate is inactive" do
      let(:drive_uuid) { drive_with_blank_rego.uuid }
      let(:params) { { trade_plate_uuid: inactive_trade_plate.uuid } }

      it "returns an error" do
        patch url, params: params, headers: headers

        expect(response).to have_http_status(:not_found)

        json_response = response.parsed_body
        expect(json_response["status"]["message"]).to include("Trade plate not found")
      end
    end

    context "when trade plate is expired" do
      let(:drive_uuid) { drive_with_blank_rego.uuid }
      let(:params) { { trade_plate_uuid: expired_trade_plate.uuid } }

      it "allows expired one too" do
        patch url, params: params, headers: headers

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response["status"]["message"]).to eq("Trade plate assigned successfully")
        expect(json_response["data"]["drive"]["trade_plate"]["uuid"]).to eq(expired_trade_plate.uuid)
        expect(json_response["data"]["drive"]["trade_plate"]["number"]).to eq(expired_trade_plate.number)

        # Verify the drive was updated in the database
        drive_with_blank_rego.reload
        expect(drive_with_blank_rego.trade_plate).to eq(expired_trade_plate)
      end
    end

    context "when trade plate is already in use" do
      let(:drive_uuid) { drive_with_blank_rego.uuid }
      let(:params) { { trade_plate_uuid: active_trade_plate.uuid } }

      it "allows used one too" do
        patch url, params: params, headers: headers

        expect(response).to have_http_status(:ok)

        json_response = response.parsed_body
        expect(json_response["status"]["message"]).to eq("Trade plate assigned successfully")
        expect(json_response["data"]["drive"]["trade_plate"]["uuid"]).to eq(active_trade_plate.uuid)
        expect(json_response["data"]["drive"]["trade_plate"]["number"]).to eq(active_trade_plate.number)

        # Verify the drive was updated in the database
        drive_with_blank_rego.reload
        expect(drive_with_blank_rego.trade_plate).to eq(active_trade_plate)
      end
    end

    context "when drive does not exist" do
      let(:drive_uuid) { "non-existent-uuid" }
      let(:params) { { trade_plate_uuid: active_trade_plate.uuid } }

      it "returns an error" do
        patch url, params: params, headers: headers

        expect(response).to have_http_status(:not_found)

        json_response = response.parsed_body
        expect(json_response["status"]["message"]).to eq("Drive not found")
      end
    end

    context "when dealership does not exist" do
      let(:drive_uuid) { drive_with_blank_rego.uuid }
      let(:params) { { trade_plate_uuid: active_trade_plate.uuid } }
      let(:url) { "/api/v1/dealerships/non-existent-uuid/drives/#{drive_uuid}/trade-plate" }

      it "returns an error" do
        patch url, params: params, headers: headers

        expect(response).to have_http_status(:not_found)

        json_response = response.parsed_body
        expect(json_response["status"]["message"]).to eq("Dealership not found or you don't have access to it")
      end
    end

    context "when user is not authenticated" do
      let(:drive_uuid) { drive_with_blank_rego.uuid }
      let(:params) { { trade_plate_uuid: active_trade_plate.uuid } }
      let(:headers) { {} }

      it "returns an unauthorized error" do
        patch url, params: params, headers: headers

        expect(response).to have_http_status(:unauthorized)
      end
    end
  end

  path "/api/v1/dealerships/{dealership_uuid}/drives/{drive_uuid}/trade-plate" do
    parameter name: :dealership_uuid, in: :path, type: :string, description: "Dealership UUID", required: true
    parameter name: :drive_uuid, in: :path, type: :string, description: "Drive UUID", required: true
    parameter name: 'Authorization', in: :header, type: :string, required: true, description: 'Bearer token for authentication'
    parameter name: 'Device-ID', in: :header, type: :string, required: true, description: 'Device ID for the request'

    patch "Assign trade plate to drive" do
      tags "Drives"
      description "Assigns a trade plate to a drive. The trade plate can only be assigned if the vehicle associated with the drive has a blank registration value."
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: :trade_plate_assignment, in: :body, schema: {
        type: :object,
        properties: {
          trade_plate_uuid: {
            type: :string,
            description: "UUID of the trade plate to assign",
            example: "123e4567-e89b-12d3-a456-************"
          }
        },
        required: [ "trade_plate_uuid" ]
      }

      let(:drive_uuid) { drive_with_blank_rego.uuid }

      response "200", "Trade plate assigned successfully" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 200 },
                     message: { type: :string, example: "Trade plate assigned successfully" }
                   }
                 },
                 data: {
                   type: :object,
                   properties: {
                    drive: {
                     uuid: { type: :string, example: "123e4567-e89b-12d3-a456-************" },
                     drive_type: { type: :string, example: "test_drive" },
                     status: { type: :string, example: "scheduled" },
                     sold_status: { type: :string, example: "unsold" },
                     notes: { type: :string, nullable: true },
                     expected_pickup_datetime: { type: :string, format: :datetime, nullable: true },
                     expected_return_datetime: { type: :string, format: :datetime, nullable: true },
                     start_datetime: { type: :string, format: :datetime, nullable: true },
                     end_datetime: { type: :string, format: :datetime, nullable: true },
                     start_odometer_reading: { type: :integer, nullable: true },
                     end_odometer_reading: { type: :integer, nullable: true },
                     vehicle: {
                       type: :object,
                       properties: {
                         uuid: { type: :string },
                         make: { type: :string },
                         model: { type: :string },
                         build_year: { type: :integer },
                         color: { type: :string },
                         rego: { type: :string, nullable: true },
                         display_name: { type: :string }
                       }
                     },
                     customer: {
                       type: :object,
                       nullable: true,
                       properties: {
                         uuid: { type: :string },
                         first_name: { type: :string },
                         last_name: { type: :string },
                         email: { type: :string },
                         phone_number: { type: :string },
                         full_name: { type: :string }
                       }
                     },
                     sales_person: {
                       type: :object,
                       properties: {
                         uuid: { type: :string },
                         first_name: { type: :string },
                         last_name: { type: :string },
                         email: { type: :string },
                         full_name: { type: :string }
                       }
                     },
                     sales_person_accompanying: {
                       type: :object,
                       nullable: true,
                       properties: {
                         uuid: { type: :string },
                         first_name: { type: :string },
                         last_name: { type: :string },
                         email: { type: :string },
                         full_name: { type: :string }
                       }
                     },
                     trade_plate: {
                       type: :object,
                       nullable: true,
                       properties: {
                         uuid: { type: :string },
                         number: { type: :string },
                         status: { type: :string },
                         expiry: { type: :string, format: :date, nullable: true }
                       }
                     },
                     created_at: { type: :string, format: :datetime },
                     updated_at: { type: :string, format: :datetime }
                   }
                  }
                 }
               }

        let(:trade_plate_assignment) { { trade_plate_uuid: active_trade_plate.uuid } }

        run_test! do |response|
          json_response = JSON.parse(response.body)
          expect(json_response["status"]["message"]).to eq("Trade plate assigned successfully")
          expect(json_response["data"]["drive"]["trade_plate"]["uuid"]).to eq(active_trade_plate.uuid)
        end
      end

      response "404", "Resource not found" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, examples: {
                       drive_not_found: "Drive not found",
                       trade_plate_not_found: "Trade plate not found or does not belong to this dealership"
                     } }
                   }
                 }
               }

        context "when drive is not found" do
          let(:drive_uuid) { "non-existent-uuid" }
          let(:trade_plate_assignment) { { trade_plate_uuid: active_trade_plate.uuid } }

          run_test!
        end

        context "when trade plate is not found" do
          let(:trade_plate_assignment) { { trade_plate_uuid: "non-existent-uuid" } }

          run_test!
        end
      end

      response "422", "Trade plate UUID is required" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Trade plate UUID is required" }
                   }
                 }
               }

        let(:trade_plate_assignment) { {} }

        run_test!
      end

      response "422", "Vehicle must have blank registration" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 422 },
                     message: { type: :string, example: "Trade plate can only be assigned to vehicles with blank registration" }
                   }
                 }
               }

        let!(:vehicle_with_rego) { create(:vehicle, dealership: dealership, rego: "ABC123") }
        let!(:drive_with_rego) do
          create(:drive,
                 dealership: dealership,
                 vehicle: vehicle_with_rego,
                 sales_person: sales_person,
                 drive_type: :test_drive,
                 status: :scheduled)
        end
        let(:drive_uuid) { drive_with_rego.uuid }
        let(:trade_plate_assignment) { { trade_plate_uuid: active_trade_plate.uuid } }

        run_test!
      end

      response "404", "Trade plate not found or does not belong to this dealership" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 404 },
                     message: { type: :string, example: "Trade plate not found" }
                   }
                 }
               }

        let!(:inactive_trade_plate) { create(:trade_plate, dealership: dealership, status: :inactive) }
        let(:trade_plate_assignment) { { trade_plate_uuid: inactive_trade_plate.uuid } }

        run_test!
      end

      response "401", "Unauthorized" do
        schema type: :object,
               properties: {
                 status: {
                   type: :object,
                   properties: {
                     code: { type: :integer, example: 401 },
                     message: { type: :string, example: "Email or password is incorrect." }
                   }
                 }
               }

        let(:Authorization) { "Bearer invalid-token" }
        let(:trade_plate_assignment) { { trade_plate_uuid: active_trade_plate.uuid } }

        run_test!
      end
    end
  end
end
