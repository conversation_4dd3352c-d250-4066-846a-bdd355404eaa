# frozen_string_literal: true

require "rails_helper"
require "swagger_helper"

RSpec.describe "Api::V1::Dealerships::DrivesController", type: :request do
  include_context "drive_api_shared_context"

  let!(:vehicle1) { create(:vehicle, dealership: dealership) }
  let!(:customer1) { create(:customer, dealership: dealership) }

  path "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/return-time" do
    put "Update drive return time" do
      tags "Drives"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :uuid, in: :path, type: :string, required: true, description: "Drive UUID"
      parameter name: :drive_params, in: :body, required: true, schema: {
        type: :object,
        required: [ "drive" ],
        properties: {
          drive: {
            type: :object,
            required: [ "expected_return_datetime" ],
            properties: {
              expected_return_datetime: { type: :string, format: "date-time" }
            }
          }
        }
      }

      response "200", "Return time updated successfully" do
        let(:test_drive) { create(:drive, :vehicle_out_type, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:drive_params) do
          {
            drive: {
              expected_return_datetime: 1.hour.from_now.iso8601
            }
          }
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Return time updated successfully")
          expect(json.dig("data", "drive")).to be_present
          expect(Time.zone.parse(json.dig("data", "drive", "expected_return_datetime"))).to be_within(1.second).of(Time.zone.parse(drive_params[:drive][:expected_return_datetime]))
        end
      end

      response "422", "Invalid return time" do
        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        context "when expected_return_datetime is missing" do
          let(:drive_params) do
            {
              drive: {
                expected_return_datetime: nil
              }
            }
          end

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to eq("Expected return datetime is required")
          end
        end

        context "when expected_return_datetime is invalid" do
          let(:drive_params) do
            {
              drive: {
                expected_return_datetime: "not-a-datetime"
              }
            }
          end

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to eq("Invalid datetime format: not-a-datetime")
          end
        end
      end

      response "422", "Invalid return time update" do
        let(:test_drive) { create(:drive, drive_type: :enquiry, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        context "when expected_return_datetime is missing" do
          let(:drive_params) do
            {
              drive: {
                expected_return_datetime: 2.hours.from_now.iso8601
              }
            }
          end

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to include("Return time cannot be updated for this drive type")
          end
        end

        context "when expected_return_datetime is invalid" do
          let(:drive_params) do
            {
              drive: {
                expected_return_datetime: "not-a-datetime"
              }
            }
          end

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to eq("Invalid datetime format: not-a-datetime")
          end
        end
      end


      response "404", "Drive not found" do
        let(:uuid) { "invalid-uuid" }
        let(:drive_params) do
          {
            drive: {
              expected_return_datetime: 1.hour.from_now.iso8601
            }
          }
        end
        run_test! do |response|
          expect(response.status).to eq(404)
          expect(response.parsed_body.dig("status", "message")).to eq("Drive not found")
        end
      end
    end
  end

  path "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/odometer" do
    put "Update drive odometer reading" do
      tags "Drives"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :uuid, in: :path, type: :string, required: true, description: "Drive UUID"
      parameter name: :drive_params, in: :body, required: true, schema: {
        type: :object,
        required: [ "drive" ],
        properties: {
          drive: {
            type: :object,
            properties: {
              start_odometer_reading: { type: :integer },
              end_odometer_reading: { type: :integer }
            }
          }
        }
      }

      response "200", "Odometer reading updated successfully" do
        let(:test_drive) { create(:drive, :vehicle_out_type, :scheduled, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }

        context "when updating start odometer for scheduled drive" do
          let(:drive_params) do
            {
              drive: {
                start_odometer_reading: 12345
              }
            }
          end
          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("Start odometer reading updated successfully")
            expect(json.dig("data", "drive", "start_odometer_reading")).to eq(12345)
          end
        end

        context "when updating end odometer for in-progress drive" do
          let(:in_progress_drive) { create(:drive, :vehicle_out_type, status: :in_progress, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
          let(:uuid) { in_progress_drive.uuid }
          let(:drive_params) do
            {
              drive: {
                end_odometer_reading: 12500
              }
            }
          end
          run_test! do |response|
            json = response.parsed_body
            expect(json.dig("status", "message")).to eq("End odometer reading updated successfully")
            expect(json.dig("data", "drive", "end_odometer_reading")).to eq(12500)
          end
        end
      end

      response "422", "Invalid odometer update" do
        let(:test_drive) { create(:drive, :vehicle_out_type, :scheduled, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        context "when no odometer readings provided" do
          let(:drive_params) do
            {
              drive: {
                start_odometer_reading: nil,
                end_odometer_reading: nil
              }
            }
          end

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to eq("At least one of start_odometer_reading or end_odometer_reading must be provided")
          end
        end

        context "when updating start odometer for in-progress drive" do
          let(:in_progress_drive) { create(:drive, :vehicle_out_type, status: :in_progress, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
          let(:uuid) { in_progress_drive.uuid }
          let(:drive_params) do
            {
              drive: {
                start_odometer_reading: 12345
              }
            }
          end

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to eq("Validation failed: Start odometer reading can only be updated when drive is in scheduled status")
          end
        end

        context "when updating end odometer for scheduled drive" do
          let(:drive_params) do
            {
              drive: {
                end_odometer_reading: 12500
              }
            }
          end

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to eq("Validation failed: End odometer reading can only be updated when drive is in progress")
          end
        end
      end

      response "404", "Drive not found" do
        let(:uuid) { "invalid-uuid" }
        let(:drive_params) do
          {
            drive: {
              start_odometer_reading: 12345
            }
          }
        end
        run_test! do |response|
          expect(response.status).to eq(404)
          expect(response.parsed_body.dig("status", "message")).to eq("Drive not found")
        end
      end

      response "422", "Invalid odometer update" do
        let(:test_drive) { create(:drive, :test_drive_booking, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:drive_params) do
          {
            drive: {
              start_odometer_reading: 12345
            }
          }
        end
        run_test! do |response|
          expect(response.status).to eq(422)
          expect(response.parsed_body.dig("status", "message")).to include("Odometer readings can only be updated for test drives, loans, and self loans")
        end
      end

      response "401", "Unauthorized" do
        let(:test_drive) { create(:drive, :vehicle_out_type, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:drive_params) do
          {
            drive: {
              start_odometer_reading: 12345
            }
          }
        end
        let(:Authorization) { "Bearer invalid_token" }
        run_test! do |response|
          expect(response.status).to eq(401)
        end
      end
    end
  end
  describe "PUT /api/v1/dealerships/:dealership_uuid/drives/:uuid/odometer" do
    context "when trying to update both readings at once" do
      let(:test_drive) { create(:drive, :scheduled, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
      let(:drive_params) do
        {
          drive: {
            start_odometer_reading: 12345,
            end_odometer_reading: 12500
          }
        }
      end

      it "returns validation error" do
        put "/api/v1/dealerships/#{dealership.uuid}/drives/#{test_drive.uuid}/odometer",
            headers: headers,
            params: drive_params

        expect(response).to have_http_status(:unprocessable_entity)
        expect(response.parsed_body.dig("status", "message")).to eq("Only one of start_odometer_reading or end_odometer_reading can be provided at a time")
      end
    end
  end

  path "/api/v1/dealerships/{dealership_uuid}/drives/{uuid}/reassign" do
    put "Reassign drive to different users" do
      tags "Drives"
      consumes "application/json"
      produces "application/json"
      security [ Bearer: [] ]

      parameter name: "Authorization", in: :header, type: :string, required: true, description: "Bearer token"
      parameter name: "Device-ID", in: :header, type: :string, required: true, description: "Device identifier"
      parameter name: :dealership_uuid, in: :path, type: :string, required: true, description: "Dealership UUID"
      parameter name: :uuid, in: :path, type: :string, required: true, description: "Drive UUID"
      parameter name: :reassign_params, in: :body, required: true, schema: {
        type: :object,
        properties: {
          sales_person_uuid: {
            type: :string,
            description: "UUID of the new sales person to assign to the drive",
            example: "123e4567-e89b-12d3-a456-************"
          },
          sales_person_accompanying_uuid: {
            type: :string,
            description: "UUID of the new accompanying sales person to assign to the drive",
            example: "123e4567-e89b-12d3-a456-************"
          }
        }
      }

      response "200", "Drive reassigned successfully" do
        let(:new_sales_person) { create(:user) }
        let(:new_accompanying_person) { create(:user) }
        let!(:new_sales_person_dealership) { create(:user_dealership, user: new_sales_person, dealership: dealership, role: :sales_person) }
        let!(:new_accompanying_person_dealership) { create(:user_dealership, user: new_accompanying_person, dealership: dealership, role: :sales_person) }
        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:reassign_params) do
          {
            sales_person_uuid: new_sales_person.uuid,
            sales_person_accompanying_uuid: new_accompanying_person.uuid
          }
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Drive reassigned successfully")
          expect(json.dig("data", "drive")).to be_present
          expect(json.dig("data", "drive", "sales_person", "uuid")).to eq(new_sales_person.uuid)
          expect(json.dig("data", "drive", "sales_person_accompanying", "uuid")).to eq(new_accompanying_person.uuid)
        end
      end

      response "200", "Drive reassigned with only sales person" do
        let(:new_sales_person) { create(:user) }
        let!(:new_sales_person_dealership) { create(:user_dealership, user: new_sales_person, dealership: dealership, role: :sales_person) }
        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:reassign_params) do
          {
            sales_person_uuid: new_sales_person.uuid
          }
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Drive reassigned successfully")
          expect(json.dig("data", "drive")).to be_present
          expect(json.dig("data", "drive", "sales_person", "uuid")).to eq(new_sales_person.uuid)
          expect(json.dig("data", "drive", "sales_person_accompanying")).to be_nil
        end
      end

      response "200", "Drive reassigned with only accompanying person" do
        let(:new_accompanying_person) { create(:user) }
        let!(:new_accompanying_person_dealership) { create(:user_dealership, user: new_accompanying_person, dealership: dealership, role: :sales_person) }
        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:reassign_params) do
          {
            sales_person_accompanying_uuid: new_accompanying_person.uuid
          }
        end

        run_test! do |response|
          json = response.parsed_body
          expect(json.dig("status", "message")).to eq("Drive reassigned successfully")
          expect(json.dig("data", "drive")).to be_present
          expect(json.dig("data", "drive", "sales_person", "uuid")).to eq(sales_person.uuid)
          expect(json.dig("data", "drive", "sales_person_accompanying", "uuid")).to eq(new_accompanying_person.uuid)
        end
      end

      response "422", "Validation error" do
        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }

        context "when no user UUIDs are provided" do
          let(:reassign_params) { {} }

          run_test! do |response|
            expect(response.status).to eq(422)
            expect(response.parsed_body.dig("status", "message")).to eq("At least one of sales_person_uuid or sales_person_accompanying_uuid must be provided")
          end
        end
      end

      response "404", "User not found or does not belong to this dealership" do
        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }

        context "when user does not belong to dealership" do
          let(:other_user) { create(:user) }
          let(:reassign_params) do
            {
              sales_person_uuid: other_user.uuid
            }
          end

          run_test! do |response|
            expect(response.status).to eq(404)
            expect(response.parsed_body.dig("status", "message")).to eq("User not found or does not belong to this dealership")
          end
        end

        context "when user UUID is invalid" do
          let(:reassign_params) do
            {
              sales_person_uuid: "invalid-uuid"
            }
          end

          run_test! do |response|
            expect(response.status).to eq(404)
            expect(response.parsed_body.dig("status", "message")).to eq("User not found or does not belong to this dealership")
          end
        end
      end

      response "404", "Drive not found" do
        let(:uuid) { "non-existent-uuid" }
        let(:new_sales_person) { create(:user) }
        let!(:new_sales_person_dealership) { create(:user_dealership, user: new_sales_person, dealership: dealership, role: :sales_person) }
        let(:reassign_params) do
          {
            sales_person_uuid: new_sales_person.uuid
          }
        end

        run_test!
      end

      response "401", "Unauthorized" do
        let(:test_drive) { create(:drive, dealership: dealership, vehicle: vehicle1, customer: customer1, sales_person: sales_person) }
        let(:uuid) { test_drive.uuid }
        let(:reassign_params) do
          {
            sales_person_uuid: sales_person.uuid
          }
        end
        let(:Authorization) { "Bearer invalid_token" }

        run_test!
      end
    end
  end
end
