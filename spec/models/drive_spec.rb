require 'rails_helper'

RSpec.describe Drive, type: :model do
  describe 'associations' do
    it { is_expected.to belong_to(:dealership) }
    it { is_expected.to belong_to(:vehicle).class_name('DealershipVehicle') }
    it { is_expected.to belong_to(:trade_plate).optional }
    it { is_expected.to belong_to(:customer).optional }
    it { is_expected.to belong_to(:sales_person).class_name('User') }
    it { is_expected.to belong_to(:driver_license).optional }
    it { is_expected.to belong_to(:sales_person_accompanying).class_name("User").optional }
    it { is_expected.to have_one(:initial_damage_report).class_name("DamageReport").optional.dependent(:destroy) }
    it { is_expected.to have_one(:final_damage_report).class_name("DamageReport").optional.dependent(:destroy) }
    it { is_expected.to have_many(:waypoints).class_name("GpsLocation").dependent(:destroy) }
  end

  describe 'enums' do
    it { is_expected.to define_enum_for(:drive_type).with_values(test_drive: 0, enquiry: 1, loan: 2, appraisal: 3, loan_booking: 4, test_drive_booking: 5, self_loan: 6).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:status).with_values(scheduled: 0, in_progress: 1, completed: 2, cancelled: 3, draft: 4, deleted: 5).backed_by_column_of_type(:integer) }
    it { is_expected.to define_enum_for(:sold_status).with_values(unsold: 0, sold: 1).backed_by_column_of_type(:integer) }
  end

  describe 'validations' do
    context 'timestamps' do
      it 'ensures start_datetime is not in the future' do
        drive = build(:drive, start_datetime: 1.hour.from_now)
        expect(drive).not_to be_valid
        expect(drive.errors[:start_datetime]).to include('cannot be in the future')
      end

      it 'ensures end_datetime is not in the future' do
        drive = build(:drive, end_datetime: 1.hour.from_now)
        expect(drive).not_to be_valid
        expect(drive.errors[:end_datetime]).to include('cannot be in the future')
      end

      it 'ensures end_datetime is after start_datetime' do
        drive = build(:drive, start_datetime: Time.current, end_datetime: 1.hour.ago)
        expect(drive).not_to be_valid
        expect(drive.errors[:end_datetime]).to include('must be after start time')
      end
    end

    context 'odometer readings' do
      it 'ensures odometer readings are positive' do
        drive = build(:drive, start_odometer_reading: -1, end_odometer_reading: -1)
        expect(drive).not_to be_valid
        expect(drive.errors[:start_odometer_reading]).to include('must be a positive number')
        expect(drive.errors[:end_odometer_reading]).to include('must be a positive number')
      end

      it 'ensures end odometer reading is greater than start odometer reading' do
        drive = build(:drive, start_odometer_reading: 100, end_odometer_reading: 50)
        expect(drive).not_to be_valid
        expect(drive.errors[:end_odometer_reading]).to include('must be greater than start odometer reading')
      end

      context 'when updating odometer readings' do
        let(:drive) { create(:drive, :vehicle_out_type, status: Drive.statuses[:scheduled]) }

        it 'allows updating start odometer when drive is scheduled' do
          drive.start_odometer_reading = 12345
          expect(drive).to be_valid
        end

        it 'prevents updating start odometer when drive is not scheduled' do
          drive.update!(status: :in_progress)
          drive.start_odometer_reading = 12345
          expect(drive).not_to be_valid
          expect(drive.errors[:start_odometer_reading]).to include('can only be updated when drive is in scheduled status')
        end

        it 'allows updating end odometer when drive is in progress' do
          drive.update!(status: :in_progress)
          drive.end_odometer_reading = 12500
          expect(drive).to be_valid
        end

        it 'prevents updating end odometer when drive is not in progress' do
          drive.end_odometer_reading = 12500
          expect(drive).not_to be_valid
          expect(drive.errors[:end_odometer_reading]).to include('can only be updated when drive is in progress')
        end
      end
    end
  end
  describe 'scopes' do
    describe '.start_datetime_between' do
      let!(:drive1) { create(:drive, start_datetime: Date.parse('2024-01-15').beginning_of_day) }
      let!(:drive2) { create(:drive, start_datetime: Date.parse('2024-01-20').beginning_of_day) }
      let!(:drive3) { create(:drive, start_datetime: Date.parse('2024-01-25').beginning_of_day) }

      context 'with start_date only' do
        it 'returns drives with start_datetime on or after the start_date' do
          result = Drive.start_datetime_between('2024-01-20', nil)
          expect(result).to include(drive2, drive3)
          expect(result).not_to include(drive1)
        end
      end

      context 'with end_date only' do
        it 'returns drives with start_datetime on or before the end_date' do
          result = Drive.start_datetime_between(nil, '2024-01-20')
          expect(result).to include(drive1, drive2)
          expect(result).not_to include(drive3)
        end
      end

      context 'with both start_date and end_date' do
        it 'returns drives with start_datetime between the dates' do
          result = Drive.start_datetime_between('2024-01-18', '2024-01-22')
          expect(result).to include(drive2)
          expect(result).not_to include(drive1, drive3)
        end
      end

      context 'with nil parameters' do
        it 'returns all drives when both dates are nil' do
          result = Drive.start_datetime_between(nil, nil)
          expect(result).to include(drive1, drive2, drive3)
        end
      end

      context 'with invalid date format in date filter' do
        it 'raises InvalidInput error for invalid start_date' do
          expect {
            Drive.start_datetime_between('invalid-date', nil)
          }.to raise_error(Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format")
        end

        it 'raises InvalidInput error for invalid end_date' do
          expect {
            Drive.start_datetime_between(nil, 'invalid-date')
          }.to raise_error(Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format")
        end

        it 'raises InvalidInput error for both invalid dates' do
          expect {
            Drive.start_datetime_between('bad-start', 'bad-end')
          }.to raise_error(Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format")
        end

        context 'with invalid date format in updated_at filter' do
          it 'raises InvalidInput error for invalid start_date' do
            expect {
              Drive.updated_between_dates('invalid-date', nil)
            }.to raise_error(Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format")
          end

          it 'raises InvalidInput error for invalid end_date' do
            expect {
              Drive.updated_between_dates(nil, 'invalid-date')
            }.to raise_error(Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format")
          end

          it 'raises InvalidInput error for both invalid dates' do
            expect {
              Drive.updated_between_dates('bad-start', 'bad-end')
            }.to raise_error(Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format")
          end
        end
      end
    end

    describe 'default_scope' do
      let!(:drive_scheduled) { create(:drive, status: :scheduled) }
      let!(:drive_in_progress) { create(:drive, status: :in_progress) }
      let!(:drive_deleted) { create(:drive, status: :deleted) }

      it 'excludes drives with status deleted by default' do
        drives = Drive.all
        expect(drives).to include(drive_scheduled, drive_in_progress)
        expect(drives).not_to include(drive_deleted)
      end

      it 'includes deleted drives when unscoped' do
        drives = Drive.unscoped.all
        expect(drives).to include(drive_scheduled, drive_in_progress, drive_deleted)
      end
    end
    describe '.pickup_between_dates' do
      let!(:drive1) { create(:drive, expected_pickup_datetime: Date.parse('2024-01-15').beginning_of_day) }
      let!(:drive2) { create(:drive, expected_pickup_datetime: Date.parse('2024-01-20').beginning_of_day) }
      let!(:drive3) { create(:drive, expected_pickup_datetime: Date.parse('2024-01-25').beginning_of_day) }

      context 'with start_date only' do
        it 'returns drives with expected_pickup_datetime on or after the start_date' do
          result = Drive.pickup_between_dates('2024-01-20', nil)
          expect(result).to include(drive2, drive3)
          expect(result).not_to include(drive1)
        end
      end

      context 'with end_date only' do
        it 'returns drives with expected_pickup_datetime on or before the end_date' do
          result = Drive.pickup_between_dates(nil, '2024-01-20')
          expect(result).to include(drive1, drive2)
          expect(result).not_to include(drive3)
        end
      end

      context 'with both start_date and end_date' do
        it 'returns drives with expected_pickup_datetime between the dates' do
          result = Drive.pickup_between_dates('2024-01-18', '2024-01-22')
          expect(result).to include(drive2)
          expect(result).not_to include(drive1, drive3)
        end
      end

      context 'with nil parameters' do
        it 'returns all drives when both dates are nil' do
          result = Drive.pickup_between_dates(nil, nil)
          expect(result).to include(drive1, drive2, drive3)
        end
      end

      context 'with invalid date format' do
        it 'raises InvalidInput error for invalid start_date' do
          expect {
            Drive.pickup_between_dates('invalid-date', nil)
          }.to raise_error(Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format")
        end

        it 'raises InvalidInput error for invalid end_date' do
          expect {
            Drive.pickup_between_dates(nil, 'invalid-date')
          }.to raise_error(Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format")
        end

        it 'raises InvalidInput error for both invalid dates' do
          expect {
            Drive.pickup_between_dates('bad-start', 'bad-end')
          }.to raise_error(Errors::InvalidInput, "Invalid date format. Use YYYY-MM-DD format")
        end
      end
    end

    describe 'other scopes' do
      let(:dealership) { create(:dealership) }
      let(:sales_person) { create(:user) }
      let!(:sales_person_dealership) { create(:user_dealership, user: sales_person, dealership: dealership, role: :sales_person) }
      let!(:active_drive) { create(:drive, status: :in_progress, drive_type: :enquiry, dealership: dealership, sales_person: sales_person) }
      let!(:scheduled_drive) { create(:drive, status: :scheduled, drive_type: :loan, dealership: dealership, sales_person: sales_person) }
      let!(:booking_drive) { create(:drive, status: :scheduled, drive_type: :test_drive_booking, dealership: dealership, sales_person: sales_person) }
      let!(:vehicle_out_drive) { create(:drive, status: :completed, drive_type: :test_drive, dealership: dealership, sales_person: sales_person) }

      describe '.active' do
        it 'returns only drives with in_progress status' do
          result = Drive.active
          expect(result).to include(active_drive)
          expect(result).not_to include(scheduled_drive, booking_drive, vehicle_out_drive)
        end
      end

      describe '.bookings' do
        it 'returns only booking type drives' do
          result = Drive.bookings
          expect(result).to include(booking_drive)
          expect(result).not_to include(active_drive, scheduled_drive, vehicle_out_drive)
        end
      end

      describe '.vehicle_out_type_drives' do
        it 'returns only vehicle out type drives' do
          result = Drive.vehicle_out_type_drives
          expect(result).to include(vehicle_out_drive)
          expect(result).not_to include(active_drive, booking_drive)
        end
      end

      describe 'filter scopes' do
        let!(:sold_drive) { create(:drive, sold_status: :sold, dealership: dealership) }
        let!(:loan_drive) { create(:drive, drive_type: :loan, dealership: dealership) }

        it '.filter_by_status filters by status' do
          result = Drive.filter_by_status(:scheduled)
          expect(result).to include(scheduled_drive)
          expect(result).not_to include(active_drive)
        end

        it '.filter_by_drive_type filters by drive type' do
          result = Drive.filter_by_drive_type(:loan)
          expect(result).to include(loan_drive)
          expect(result).not_to include(vehicle_out_drive)
        end

        it '.filter_by_sold_status filters by sold status' do
          result = Drive.filter_by_sold_status(:sold)
          expect(result).to include(sold_drive)
          expect(result).not_to include(active_drive)
        end
      end

      describe 'association filter scopes' do
        let(:sales_person) { create(:user) }
        let!(:sales_person_dealership) { create(:user_dealership, user: sales_person, dealership: dealership, role: :sales_person) }
        let(:customer) { create(:customer, dealership: dealership) }
        let(:vehicle) { create(:vehicle, dealership: dealership) }
        let(:trade_plate) { create(:trade_plate, dealership: dealership) }
        let!(:user_drive) { create(:drive, sales_person: sales_person, dealership: dealership) }
        let!(:customer_drive) { create(:drive, customer: customer, dealership: dealership) }
        let!(:vehicle_drive) { create(:drive, vehicle: vehicle, dealership: dealership) }
        let!(:trade_plate_drive) { create(:drive, trade_plate: trade_plate, dealership: dealership) }

        it '.by_salesperson filters by sales person uuid' do
          result = Drive.by_salesperson(sales_person.uuid)
          expect(result).to include(user_drive)
        end

        it '.by_customer filters by customer uuid' do
          result = Drive.by_customer(customer.uuid)
          expect(result).to include(customer_drive)
        end

        it '.by_vehicle filters by vehicle uuid' do
          result = Drive.by_vehicle(vehicle.uuid)
          expect(result).to include(vehicle_drive)
        end

        it '.by_trade_plate filters by trade plate uuid' do
          result = Drive.by_trade_plate(trade_plate.uuid)
          expect(result).to include(trade_plate_drive)
        end
      end
    end
  end

  describe 'validations' do
    describe 'sales_person_belongs_to_dealership' do
      let(:dealership) { create(:dealership) }
      let(:other_dealership) { create(:dealership) }
      let(:user) { create(:user) }
      let(:vehicle) { create(:vehicle, dealership: dealership) }

      before do
        create(:user_dealership, user: user, dealership: dealership, role: :sales_person)
      end

      it 'is valid when sales person belongs to the dealership' do
        drive = build(:drive, dealership: dealership, vehicle: vehicle, sales_person: user)
        expect(drive).to be_valid
      end

      it 'is invalid when sales person does not belong to the dealership' do
        other_user = create(:user)
        create(:user_dealership, user: other_user, dealership: other_dealership, role: :sales_person)

        drive = build(:drive, dealership: dealership, vehicle: vehicle, sales_person: other_user)
        expect(drive).not_to be_valid
        expect(drive.errors[:sales_person]).to include("must belong to the vehicle's dealership")
      end
    end

    describe 'expected_return_datetime validation' do
      it 'ensures expected_return_datetime is after expected_pickup_datetime' do
        drive = build(:drive,
          expected_pickup_datetime: 1.hour.from_now,
          expected_return_datetime: 30.minutes.from_now
        )
        expect(drive).not_to be_valid
        expect(drive.errors[:expected_return_datetime]).to include('must be after pickup time')
      end

      it 'is valid when expected_return_datetime is after expected_pickup_datetime' do
        drive = build(:drive,
          expected_pickup_datetime: 1.hour.from_now,
          expected_return_datetime: 2.hours.from_now
        )
        expect(drive).to be_valid
      end
    end

    describe 'validate_return_time on update' do
      let(:drive) { create(:drive, drive_type: :enquiry) }

      it 'prevents updating return time for invalid drive types' do
        drive.expected_return_datetime = 2.hours.from_now
        expect(drive).not_to be_valid
        expect(drive.errors[:base]).to include('Return time cannot be updated for this drive type')
      end

      it 'allows updating return time for valid drive types' do
        drive.update!(drive_type: :test_drive)
        drive.expected_return_datetime = 2.hours.from_now
        expect(drive).to be_valid
      end
    end

    describe 'validate_odometer_update_status for non-vehicle-out drives' do
      let(:drive) { create(:drive, drive_type: :enquiry) }

      it 'prevents updating odometer readings for non-vehicle-out drive types' do
        drive.start_odometer_reading = 1000
        expect(drive).not_to be_valid
        expect(drive.errors[:base]).to include('Odometer readings can only be updated for test drives, loans, and self loans')
      end
    end
  end

  describe 'instance methods' do
    describe '#cancel_with_reason!' do
      let(:drive) { create(:drive, status: :scheduled) }

      it 'cancels the drive with a reason' do
        drive.cancel_with_reason!('Customer cancelled')
        expect(drive.status).to eq('cancelled')
        expect(drive.cancel_reason).to eq('Customer cancelled')
      end

      it 'raises error when reason is blank' do
        expect {
          drive.cancel_with_reason!('')
        }.to raise_error(Errors::InvalidInput, 'Cancel reason is required')
      end

      it 'raises error when drive is already completed' do
        drive.update!(status: :completed)
        expect {
          drive.cancel_with_reason!('Test reason')
        }.to raise_error(Errors::InvalidInput, 'Cancellation not allowed')
      end
    end

    describe '#mark_completed' do
      let(:dealership) { create(:dealership) }
      let(:vehicle) { create(:vehicle, dealership: dealership, last_known_odometer_km: 10000) }
      let(:drive) { create(:drive, dealership: dealership, vehicle: vehicle, drive_type: :test_drive, status: :in_progress, end_odometer_reading: 10100) }

      context 'when drive is valid for completion' do
        it 'marks the drive as completed' do
          drive.mark_completed
          expect(drive.status).to eq('completed')
          expect(drive.end_datetime).to be_present
          expect(drive.end_datetime).to be_within(1.second).of(Time.current)
        end

        it 'updates vehicle last known odometer' do
          drive.mark_completed
          expect(vehicle.reload.last_known_odometer_km).to eq(10100)
        end

        it 'accepts optional notes' do
          notes = 'Drive completed successfully'
          drive.mark_completed(notes)
          expect(drive.notes).to eq(notes)
        end

        it 'does not overwrite existing notes when no notes provided' do
          existing_notes = 'Existing notes'
          drive.update!(notes: existing_notes)
          drive.mark_completed
          expect(drive.notes).to eq(existing_notes)
        end

        context 'with waypoints' do
          let!(:waypoint1) { create(:gps_location, trackable: drive, latitude: -33.8688, longitude: 151.2093, accuracy: 5.0) }
          let!(:waypoint2) { create(:gps_location, trackable: drive, latitude: -33.8700, longitude: 151.2100, accuracy: 3.0) }

          before do
            vehicle.update!(last_known_location: create(:gps_location, trackable: vehicle, latitude: -33.8600, longitude: 151.2000, accuracy: 10.0))
          end

          it 'updates vehicle last known location from last waypoint' do
            expect { drive.mark_completed }.to change { vehicle.reload.last_known_location&.latitude }.to(-33.8700)
            expect(vehicle.last_known_location.longitude).to eq(151.2100)
            expect(vehicle.last_known_location.accuracy).to eq(3.0)
          end
        end

        context 'with final damage report' do
          let!(:final_damage_report) { create(:damage_report, drive: drive, report_type: DamageReport::FINAL, description: 'Minor scratch on door') }

          before do
            vehicle.update!(last_damage_report: create(:damage_report, vehicle: vehicle, report_type: DamageReport::VEHICLE, description: 'Major scratch'))
          end

          it 'creates a vehicle damage report from final damage report' do
            expect { drive.mark_completed }.to change { DamageReport.where(vehicle: vehicle, report_type: DamageReport::VEHICLE).count }.by(0)

            vehicle_reports = DamageReport.where(vehicle: vehicle, report_type: DamageReport::VEHICLE)
            new_report = vehicle_reports.order(:created_at).last
            expect(new_report.description).to eq('Minor scratch on door')
          end

          it 'updates vehicle last damage report' do
            drive.mark_completed
            expect(vehicle.reload.last_damage_report).to be_present
            expect(vehicle.last_damage_report.description).to eq('Minor scratch on door')
          end
        end
      end

      context 'when drive type is not vehicle out type' do
        let(:booking_drive) { create(:drive, drive_type: :test_drive_booking, status: :in_progress) }

        it 'raises error for booking type drives' do
          expect {
            booking_drive.mark_completed
          }.to raise_error(Errors::InvalidInput, 'This drive cannot be marked as completed')
        end

        it 'raises error for enquiry type drives' do
          enquiry_drive = create(:drive, drive_type: :enquiry, status: :in_progress)
          expect {
            enquiry_drive.mark_completed
          }.to raise_error(Errors::InvalidInput, 'This drive cannot be marked as completed')
        end
      end

      context 'when drive is not in progress' do
        it 'raises error for scheduled drives' do
          scheduled_drive = create(:drive, drive_type: :test_drive, status: :scheduled)
          expect {
            scheduled_drive.mark_completed
          }.to raise_error(Errors::InvalidInput, 'Only in-progress drives can be marked as completed')
        end

        it 'raises error for completed drives' do
          completed_drive = create(:drive, drive_type: :test_drive, status: :completed)
          expect {
            completed_drive.mark_completed
          }.to raise_error(Errors::InvalidInput, 'Only in-progress drives can be marked as completed')
        end

        it 'raises error for cancelled drives' do
          cancelled_drive = create(:drive, drive_type: :test_drive, status: :cancelled)
          expect {
            cancelled_drive.mark_completed
          }.to raise_error(Errors::InvalidInput, 'Only in-progress drives can be marked as completed')
        end
      end

      context 'with different vehicle out drive types' do
        Drive::VEHICLE_OUT_DRIVE_TYPES.each do |drive_type|
          it "allows completion for #{drive_type} drives" do
            test_drive = create(:drive, drive_type: drive_type, status: :in_progress, end_odometer_reading: 10100)
            expect { test_drive.mark_completed }.not_to raise_error
            expect(test_drive.reload.status).to eq('completed')
          end
        end
      end

      it 'raises error when drive is already cancelled' do
        drive.update!(status: :cancelled)
        expect {
          drive.cancel_with_reason!('Test reason')
        }.to raise_error(Errors::InvalidInput, 'Cancellation not allowed')
      end

      it 'raises error when end odometer reading is not provided' do
        drive.update!(end_odometer_reading: nil)
        expect {
          drive.mark_completed
        }.to raise_error(Errors::InvalidInput, 'End odometer reading is required')
      end

      describe 'CopyDamageReportAttachmentsJob enqueueing' do
        before do
          allow(CopyDamageReportAttachmentsJob).to receive(:perform_later)
        end

        context 'when final damage report has media files and vehicle damage report is created' do
          let!(:final_damage_report) { create(:damage_report, :with_media_files, drive: drive, report_type: DamageReport::FINAL, description: 'Final damage') }

          it 'enqueues CopyDamageReportAttachmentsJob with correct parameters' do
            drive.mark_completed

            vehicle_damage_report = DamageReport.where(vehicle: vehicle, report_type: DamageReport::VEHICLE).last
            expect(CopyDamageReportAttachmentsJob).to have_received(:perform_later).with(
              final_damage_report.id,
              vehicle_damage_report.id
            )
          end
        end

        context 'when final damage report exists but has no media files' do
          let!(:final_damage_report) do
            create(:damage_report, drive: drive, report_type: DamageReport::FINAL, description: 'Final damage')
          end

          it 'does not enqueue CopyDamageReportAttachmentsJob' do
            drive.mark_completed

            expect(CopyDamageReportAttachmentsJob).not_to have_received(:perform_later)
          end
        end

        context 'when final damage report does not exist' do
          it 'does not enqueue CopyDamageReportAttachmentsJob' do
            drive.mark_completed

            expect(CopyDamageReportAttachmentsJob).not_to have_received(:perform_later)
          end
        end

        context 'when final damage report exists with media files but vehicle damage report creation fails' do
          let!(:final_damage_report) {
            create(:damage_report, :with_media_files, drive: drive, report_type: DamageReport::FINAL, description: 'Final damage')
          }

          before do
            allow(DamageReport).to receive(:create!).and_raise(ActiveRecord::RecordInvalid)
          end

          it 'does not enqueue CopyDamageReportAttachmentsJob when transaction fails' do
            expect { drive.mark_completed }.to raise_error(ActiveRecord::RecordInvalid)

            expect(CopyDamageReportAttachmentsJob).not_to have_received(:perform_later)
          end
        end
      end
    end

    describe '#to_param' do
      it 'returns the uuid' do
        drive = create(:drive)
        expect(drive.to_param).to eq(drive.uuid)
      end
    end
  end
  describe '.eligible_for_return' do
    let(:dealership) { create(:dealership) }
    let(:sales_person) { create(:user) }
    let!(:sales_person_dealership) { create(:user_dealership, user: sales_person, dealership: dealership, role: :sales_person) }

    let!(:eligible_drive1) { create(:drive, drive_type: :test_drive, status: :in_progress, dealership: dealership, sales_person: sales_person) }
    let!(:eligible_drive2) { create(:drive, drive_type: :loan, status: :in_progress, dealership: dealership, sales_person: sales_person) }
    let!(:eligible_drive3) { create(:drive, drive_type: :self_loan, status: :in_progress, dealership: dealership, sales_person: sales_person) }
    let!(:not_eligible_status) { create(:drive, drive_type: :test_drive, status: :completed, dealership: dealership, sales_person: sales_person) }
    let!(:not_eligible_type) { create(:drive, drive_type: :enquiry, status: :in_progress, dealership: dealership, sales_person: sales_person) }
    let!(:deleted_drive) { create(:drive, drive_type: :test_drive, status: :deleted, dealership: dealership, sales_person: sales_person) }

    it 'returns only drives with vehicle out drive types and in_progress status' do
      result = Drive.eligible_for_return
      expect(result).to include(eligible_drive1, eligible_drive2, eligible_drive3)
      expect(result).not_to include(not_eligible_status, not_eligible_type)
    end

    it 'does not include deleted drives due to default scope' do
      result = Drive.eligible_for_return
      expect(result).not_to include(deleted_drive)
    end
  end
end
