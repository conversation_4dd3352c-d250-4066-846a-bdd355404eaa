{"info": {"_postman_id": "22710-be5f9b8e-b758-4253-9619-93aed4db9513", "name": "DealerDrive", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"id": "5d49237a-2a7d-42f7-89dc-1634fb828b1d", "exec": ["pm.collectionVariables.set(\"temp-token\", pm.response.json().data[\"temporary_token\"]);", ""], "type": "text/javascript", "packages": {}}}], "id": "22710-11a57cac-6b28-42a4-915f-e22abc1a28e2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Device-ID", "value": "{{device_id}}", "type": "text", "disabled": true}, {"key": "App-Version", "value": "{{app_version}}", "type": "text", "disabled": true}, {"key": "App-Build-Number", "value": "{{build}}", "type": "text", "disabled": true}, {"key": "Device-OS", "value": "{{os}}", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"user\":\n    {\n        \"email\": \"<EMAIL>\",\n        \"password\": \"Regional123!\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/login", "host": ["{{host}}"], "path": ["api", "v1", "auth", "login"]}}, "response": []}, {"name": "2FA Verify", "event": [{"listen": "test", "script": {"id": "7e43ea0e-1926-434a-919a-9dcf6ee2df1c", "exec": ["pm.collectionVariables.set(\"access-token\", pm.response.json().data[\"access_token\"]);", "pm.collectionVariables.set(\"refresh-token\", pm.response.json().data[\"refresh_token\"]);", ""], "type": "text/javascript", "packages": {}}}], "id": "22710-4026c005-2225-4ed2-aa01-6dd3006aad0d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Device-ID", "value": "{{device_id}}", "type": "text"}, {"key": "App-Version", "value": "{{app_version}}", "type": "text"}, {"key": "App-Build-Number", "value": "{{build}}", "type": "text"}, {"key": "Device-OS", "value": "{{os}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"temporary_token\": \"{{temp-token}}\",\n    \"otp\": \"785768\",\n    \"method\": \"sms\" // to be only sent when a different method than the preferred one is used\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/verify-2fa", "host": ["{{host}}"], "path": ["api", "v1", "auth", "verify-2fa"]}}, "response": []}, {"name": "resend otp", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript", "packages": {}, "id": "1408589d-b7ed-4416-a4dc-d421db8d1f2d"}}], "id": "22710-4cb94fe3-bddd-4f37-8317-4483ba8a9e07", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Device-ID", "value": "{{device_id}}", "type": "text", "disabled": true}, {"key": "App-Version", "value": "{{app_version}}", "type": "text", "disabled": true}, {"key": "App-Build-Number", "value": "{{build}}", "type": "text", "disabled": true}, {"key": "Device-OS", "value": "{{os}}", "type": "text", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"temporary_token\": \"{{temp-token}}\",\n    \"method\": \"email\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/resend-otp", "host": ["{{host}}"], "path": ["api", "v1", "auth", "resend-otp"]}}, "response": []}, {"name": "RefreshToken", "event": [{"listen": "test", "script": {"exec": ["pm.collectionVariables.set(\"access-token\", pm.response.json().data[\"access_token\"]);", "pm.collectionVariables.set(\"refresh-token\", pm.response.json().data[\"refresh_token\"]);", ""], "type": "text/javascript", "packages": {}, "id": "d88c3924-2aec-4dd8-8e06-41829474ba3c"}}], "id": "22710-9e3b0f30-e5e5-4dee-9c18-e9f01e61160f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"refresh_token\": \"{{refresh-token}}\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/refresh", "host": ["{{host}}"], "path": ["api", "v1", "auth", "refresh"]}}, "response": []}, {"name": "profile", "id": "22710-78f1c56b-3153-4a9d-b419-09e351f81670", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/profile", "host": ["{{host}}"], "path": ["api", "v1", "profile"]}}, "response": []}, {"name": "devices", "id": "22710-8938ba01-aa63-4ebb-b964-faa391773f87", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/devices", "host": ["{{host}}"], "path": ["api", "v1", "devices"]}}, "response": []}, {"name": "Logout", "id": "22710-1e4b64d1-06b6-4030-b889-badba9c99870", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}", "type": "text"}], "url": {"raw": "{{host}}/api/v1/auth/logout", "host": ["{{host}}"], "path": ["api", "v1", "auth", "logout"]}}, "response": []}, {"name": "LogoutAllDevices", "id": "22710-8fd282d4-ff2a-4e5b-9d17-1fd14905a2b0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}", "type": "text"}], "url": {"raw": "{{host}}/api/v1/devices", "host": ["{{host}}"], "path": ["api", "v1", "devices"]}}, "response": []}, {"name": "ProfilePhoto", "id": "22710-dc6481d7-eaca-4484-808e-e992999ac556", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"type": "file", "key": "photo", "src": "/Users/<USER>/Temp/CleanShot 2025-06-18 at <EMAIL>"}]}, "url": {"raw": "{{host}}/api/v1/profile/photo", "host": ["{{host}}"], "path": ["api", "v1", "profile", "photo"]}}, "response": []}, {"name": "DeletePhoto", "id": "22710-c9d25e32-c7ac-4592-b67d-214a5af33265", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/profile/photo", "host": ["{{host}}"], "path": ["api", "v1", "profile", "photo"]}}, "response": []}, {"name": "me", "id": "22710-b4a82c62-8173-4df6-8f7c-3193de821dcd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/users/me", "host": ["{{host}}"], "path": ["api", "v1", "users", "me"]}}, "response": []}, {"name": "ChangePassword", "id": "22710-2948e4a3-2ed0-4d08-9da3-3ab8b360f70e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"current_password\": \"Regional1234!\",\n    \"new_password\": \"Regional123!\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/change-password", "host": ["{{host}}"], "path": ["api", "v1", "auth", "change-password"]}}, "response": []}, {"name": "ForgotPassword", "event": [{"listen": "test", "script": {"id": "a73fde7a-49cf-4cf2-858b-c15ac21f133c", "exec": ["pm.collectionVariables.set(\"temp-token\", pm.response.json().data[\"temporary_token\"]);", ""], "type": "text/javascript", "packages": {}}}], "id": "22710-92dc14fd-8f15-4d86-b900-a4fe13030dd0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/forgot-password", "host": ["{{host}}"], "path": ["api", "v1", "auth", "forgot-password"]}}, "response": []}, {"name": "verify-reset-code", "event": [{"listen": "test", "script": {"id": "7b42c0d1-1e46-413e-9bf0-9aa8913ea132", "exec": ["pm.collectionVariables.set(\"temp-token\", pm.response.json().data[\"temporary_token\"]);"], "type": "text/javascript", "packages": {}}}], "id": "22710-5fe178f4-0124-42f4-b387-c40af57d6d80", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"temporary_token\": \"{{temp-token}}\",\n    \"reset_code\": \"913381\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/verify-reset-code", "host": ["{{host}}"], "path": ["api", "v1", "auth", "verify-reset-code"]}}, "response": []}, {"name": "ResetPassword", "id": "22710-d3cb1ffd-7746-4502-a406-89775689a759", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "<PERSON><PERSON><PERSON><PERSON>", "disabled": true}], "body": {"mode": "raw", "raw": "{\n    \"temporary_token\": \"{{temp-token}}\",\n    \"new_password\": \"Regional123!\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/reset-password", "host": ["{{host}}"], "path": ["api", "v1", "auth", "reset-password"]}}, "response": []}, {"name": "devices update", "id": "22710-2c53c552-7d74-426f-8e07-a4f64f07eb7f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"device_os\": \"ios\",\n    \"device_os_version\": \"17.2.1\",\n    \"app_version\": \"2.0.0\",\n    \"app_build_number\": \"200\",\n    \"fcm_token\": \"fcm-token-123\",\n    \"device_name\": \"iPhone 15 Pro\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/devices", "host": ["{{host}}"], "path": ["api", "v1", "devices"]}}, "response": []}, {"name": "profile update", "id": "22710-48027325-0944-4354-802e-e64e67b33d3e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"preferred_2fa\": \"sms\",\n    \"job_title\": \"Senior Technician\",\n    \"preferred_language\": \"english\",\n    \"time_zone\": \"melbourne\",\n    \"onboarding_completed\": true\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/profile", "host": ["{{host}}"], "path": ["api", "v1", "profile"]}}, "response": []}, {"name": "GetDriverLicense", "id": "22710-bb8218d8-2c29-401a-a4e0-91ca6b2f2cb3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/users/me/documents/driving-license", "host": ["{{host}}"], "path": ["api", "v1", "users", "me", "documents", "driving-license"]}}, "response": []}, {"name": "SaveDL", "id": "22710-d235f724-bdd0-4406-8998-c0e06f534626", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"licence_number\": \"DL*********\",\n    \"expiry_date\": \"2025-12-31\",\n    \"issue_date\": \"2020-01-01\",\n    \"category\": \"C\",\n    \"issuing_country\": \"au\",\n    \"issuing_state\": \"NSW\",\n    \"full_name\": \"<PERSON>\",\n    \"date_of_birth\": \"1991-01-01\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/users/me/documents/driving-license", "host": ["{{host}}"], "path": ["api", "v1", "users", "me", "documents", "driving-license"]}}, "response": []}, {"name": "DeleteDL", "id": "22710-ba712242-eb4e-4487-bbdd-d2a6963d7d36", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/users/me/documents/driving-license", "host": ["{{host}}"], "path": ["api", "v1", "users", "me", "documents", "driving-license"]}}, "response": []}, {"name": "DLImage", "id": "22710-c3e124e9-ab4c-4d8b-b660-3d204c85c60c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "formdata", "formdata": [{"key": "image_type", "value": "front", "type": "text", "uuid": "e76ae103-a162-40c2-b609-11f9ccbe8aa0"}, {"key": "image", "type": "file", "uuid": "b170e737-74ef-471c-b0e7-69d093430256", "src": "/Users/<USER>/Temp/CleanShot 2025-06-16 at <EMAIL>"}]}, "url": {"raw": "{{host}}/api/v1/users/me/documents/driving-license-image", "host": ["{{host}}"], "path": ["api", "v1", "users", "me", "documents", "driving-license-image"]}}, "response": []}, {"name": "DeleteDLImage", "id": "22710-0f5cfa31-9ce8-470e-9d4b-0d20ec80b3cb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"image_type\": \"back\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/users/me/documents/driving-license-image", "host": ["{{host}}"], "path": ["api", "v1", "users", "me", "documents", "driving-license-image"]}}, "response": []}, {"name": "setup 2FA", "id": "22710-196fd6d9-baf2-40d0-8fed-0b61474ccc94", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/auth/setup-2fa", "host": ["{{host}}"], "path": ["api", "v1", "auth", "setup-2fa"]}}, "response": []}, {"name": "verify 2FA Setup", "id": "22710-24c0678c-892c-4c0a-855a-dd12d31c60f3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"otp\": \"032914\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/verify-2fa-setup", "host": ["{{host}}"], "path": ["api", "v1", "auth", "verify-2fa-setup"]}}, "response": []}, {"name": "change 2FA method", "id": "22710-d3988c71-0796-401b-a99a-f7ea7f3d0615", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"temporary_token\": \"{{temp-token}}\",\n    \"method\": \"email\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/auth/request-2fa-method", "host": ["{{host}}"], "path": ["api", "v1", "auth", "request-2fa-method"]}}, "response": []}, {"name": "trade-plates", "id": "22710-cfd4dbab-c950-44ee-86f3-99a2f495b346", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/trade-plates", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "trade-plates"]}}, "response": []}, {"name": "Customers", "id": "22710-b8d7f2f0-7657-4fdd-a4ba-7bb247a4762e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/customers?per_page=1000", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "customers"], "query": [{"key": "per_page", "value": "1000"}]}}, "response": []}, {"name": "Customers Autocomplete Search", "id": "22710-367951ef-f509-4032-bcc8-d8bcb44a6c73", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/customers/search?query=John", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "customers", "search"], "query": [{"key": "query", "value": "<PERSON>"}]}}, "response": []}, {"name": "CreateCustomer", "id": "22710-8c30261f-2633-4689-a9a3-2ad39fa364e4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\" ,\n  \"email\": \"<EMAIL>\",\n  \"phone_number\": \"+61400000000\",\n  \"age\": 30,\n  \"gender\": \"male\",\n  \"company_name\": \"Acme Corp\",\n  \"postcode\": \"2000\",\n  \"suburb\": \"Sydney\",\n  \"address_line1\": \"123 Main St\",\n  \"city\": \"Sydney\",\n  \"state\": \"NSW\",\n  \"country\": \"au\" \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/customers", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "customers"]}}, "response": []}, {"name": "ShowCustomer", "id": "22710-c38a47b3-06b5-4ca3-8380-69449739b7e2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/customers/ce852131-d092-4717-a36c-6661b4dfcd85", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "customers", "ce852131-d092-4717-a36c-6661b4dfcd85"]}}, "response": []}, {"name": "UpdateCustomer", "id": "22710-753559c7-a67f-431a-b430-81456c13172a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"first_name\": \"<PERSON><PERSON>\",\n    \"last_name\": \"<PERSON><PERSON><PERSON>\" ,\n    \"email\": \"<EMAIL>\",\n    \"phone_number\": \"+61400000001\",\n    \"age\": 31,\n    \"gender\": \"male\",\n    \"company_name\": \"Acme Corp.\",\n    \"postcode\": \"2001\",\n    \"suburb\": \"Sydney\",\n    \"address_line1\": \"124 Main St\",\n    \"city\": \"Sydney\",\n    \"state\": \"NSW\",\n    \"country\": \"au\" \n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/customers/ce852131-d092-4717-a36c-6661b4dfcd85", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "customers", "ce852131-d092-4717-a36c-6661b4dfcd85"]}}, "response": []}, {"name": "usersByDealership", "id": "22710-2ea69ab8-cc17-49c5-967b-1802d08d4a9a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/users?role_type=sales_person&page=2&per_page=2", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "users"], "query": [{"key": "role_type", "value": "sales_person"}, {"key": "page", "value": "2"}, {"key": "per_page", "value": "2"}]}}, "response": []}, {"name": "dealerships", "id": "22710-fdfbef46-61d8-4b11-a8d9-d2e4833204ac", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships", "host": ["{{host}}"], "path": ["api", "v1", "dealerships"]}}, "response": []}, {"name": "user_invitations", "id": "22710-9ed84028-5af2-49d0-95ec-7ec9d49edfba", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"user\": {\n        \"email\": \"<EMAIL>\",\n        \"first_name\": \"Test\",\n        \"last_name\": \"User\",\n        \"phone\": \"+************\",\n        \"user_type\": \"dealership_user\",\n        \"dealership_uuid\": \"9d7267a7-108b-4e62-9d6d-77cd3d86d787\",\n        \"role_type\": \"sales_person\"\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}api/v1/user_invitations", "host": ["{{host}}api"], "path": ["v1", "user_invitations"]}}, "response": []}, {"name": "CreateBooking", "id": "22710-fd6ad63e-c452-4b06-b5a0-9524c216fcf1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"vehicle_uuid\": \"9c1f3567-dbce-4b1a-a9c2-b85a651750b6\",\n    \"drive_type\": \"test_drive_booking\",\n    \"expected_pickup_date_time\": \"2025-06-26T00:30:53.169Z\",\n    \"expected_return_date_time\": \"2025-06-27T00:30:53.169Z\",\n    // \"sales_person_uuid\": \"34c368a7-1f77-4492-8eb6-531265c6c16f\",\n    // \"customer_uuid\": \"string\",\n    \"notes\": \"string\",\n    \"customer_info\": {\n        \"first_name\": \"string\",\n        \"last_name\": \"string\",\n        \"age\": 25,\n        \"email\": \"<EMAIL>\",\n        \"phone_number\": \"string\",\n        \"gender\": \"male\",\n        \"address_line1\": \"string\",\n        \"address_line2\": \"string\",\n        \"suburb\": \"string\",\n        \"city\": \"string\",\n        \"state\": \"string\",\n        \"country\": \"string\",\n        \"postcode\": \"string\",\n        \"company_name\": \"string\",\n        \"driver_license\": {\n            \"licence_number\": \"string\",\n            \"expiry_date\": \"2025-12-31\",\n            \"issuing_state\": \"string\",\n            \"issuing_country\": \"string\",\n            \"full_name\": \"string\",\n            \"date_of_birth\": \"1990-01-01\"\n        }\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings"]}}, "response": []}, {"name": "Get Bookings", "id": "22710-5db5d53c-984d-4fcb-b81c-682a150ab042", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings?drive_type=test_drive_booking&status=scheduled&start_date=2025-06-01&end_date=2025-08-08&per_page=20&page=1&vehicle_uuid=9c1f3567-dbce-4b1a-a9c2-b85a651750b6&sales_person_uuid=b020f4ac-d938-44ac-982c-21343fdc5fec", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings"], "query": [{"key": "drive_type", "value": "test_drive_booking"}, {"key": "status", "value": "scheduled"}, {"key": "start_date", "value": "2025-06-01"}, {"key": "end_date", "value": "2025-08-08"}, {"key": "per_page", "value": "20"}, {"key": "page", "value": "1"}, {"key": "vehicle_uuid", "value": "9c1f3567-dbce-4b1a-a9c2-b85a651750b6"}, {"key": "sales_person_uuid", "value": "b020f4ac-d938-44ac-982c-21343fdc5fec"}]}}, "response": []}, {"name": "Get Booking", "id": "22710-07f74823-e2e7-4c9d-8fa9-0afa4c7b01bc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings/752b07db-21d7-47d5-bd49-71ce068468a8", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings", "752b07db-21d7-47d5-bd49-71ce068468a8"]}}, "response": []}, {"name": "Cancel Booking", "id": "22710-c45f9c44-c313-4adf-8fea-cc0891a37513", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"cancel_reason\": \"Cancel\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings/752b07db-21d7-47d5-bd49-71ce068468a8/cancel", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings", "752b07db-21d7-47d5-bd49-71ce068468a8", "cancel"]}}, "response": []}, {"name": "Update Booking", "id": "22710-d87c802f-96c6-4582-af72-1ca505ad127c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"vehicle_uuid\": \"9c1f3567-dbce-4b1a-a9c2-b85a651750b6\",\n    \"expected_pickup_date_time\": \"2025-06-26T00:30:53.169Z\",\n    \"expected_return_date_time\": \"2025-06-27T00:30:53.169Z\",\n    \"sales_person_uuid\": \"1d3dc6cc-05e5-4ae7-b105-6d7febeeb632\",\n    \"notes\": \"New Notes\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings/752b07db-21d7-47d5-bd49-71ce068468a8", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings", "752b07db-21d7-47d5-bd49-71ce068468a8"]}}, "response": []}, {"name": "CreateBookingWithDL", "id": "22710-a1dea058-3886-46be-841c-276d942cfc69", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "booking[vehicle_uuid]", "value": "bfad23a6-**************-5830bda3ff91"}, {"type": "text", "key": "booking[drive_type]", "value": "test_drive_booking"}, {"type": "text", "key": "booking[expected_pickup_date_time]", "value": "2025-06-26T00:30:53.169Z"}, {"type": "text", "key": "booking[expected_return_date_time]", "value": "2025-06-27T00:30:53.169Z"}, {"type": "text", "key": "booking[notes]", "value": "string"}, {"type": "text", "key": "booking[customer_info][first_name]", "value": "<PERSON>"}, {"type": "text", "key": "booking[customer_info][last_name]", "value": "<PERSON><PERSON>"}, {"type": "text", "key": "booking[customer_info][age]", "value": "25"}, {"type": "text", "key": "booking[customer_info][email]", "value": "<EMAIL>"}, {"type": "text", "key": "booking[customer_info][phone_number]", "value": "0987654321"}, {"type": "text", "key": "booking[customer_info][gender]", "value": "male"}, {"type": "text", "key": "booking[customer_info][address_line1]", "value": "Main Street 1"}, {"type": "text", "key": "booking[customer_info][address_line2]", "value": "<PERSON><PERSON><PERSON><PERSON>"}, {"type": "text", "key": "booking[customer_info][suburb]", "value": "Koelpin Trace"}, {"type": "text", "key": "booking[customer_info][city]", "value": "NewEast Estebanberg"}, {"type": "text", "key": "booking[customer_info][state]", "value": "New South Wales"}, {"type": "text", "key": "booking[customer_info][country]", "value": "Australia"}, {"type": "text", "key": "booking[customer_info][postcode]", "value": "4215"}, {"type": "text", "key": "booking[customer_info][company_name]", "value": "Buckridge Inc"}, {"type": "text", "key": "booking[customer_info][driver_license][licence_number]", "value": "*********"}, {"type": "text", "key": "booking[customer_info][driver_license][expiry_date]", "value": "2025-12-31"}, {"type": "text", "key": "booking[customer_info][driver_license][issuing_state]", "value": "New South Wales"}, {"type": "text", "key": "booking[customer_info][driver_license][issuing_country]", "value": "Australia"}, {"type": "text", "key": "booking[customer_info][driver_license][full_name]", "value": "<PERSON>"}, {"type": "text", "key": "booking[customer_info][driver_license][date_of_birth]", "value": "1990-01-01"}, {"type": "file", "key": "booking[customer_info][driver_license][front_image]", "src": "/Users/<USER>/Library/CloudStorage/Dropbox/Downloads/archive/images.jpeg"}, {"type": "file", "key": "booking[customer_info][driver_license][back_image]", "src": "/Users/<USER>/Library/CloudStorage/Dropbox/Downloads/archive/93995951_2626626737551587_3656803870996168704_n.jpg"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/bookings", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "bookings"]}}, "response": []}, {"name": "GetDrive", "id": "22710-788bab34-28b2-42e3-8757-5b279102b2ff", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/a18e1703-e2f2-4818-9499-9959060dacdc", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "a18e1703-e2f2-4818-9499-9959060dacdc"]}}, "response": []}, {"name": "DriveReturnTime", "id": "22710-83b9bcf1-0b25-450e-ba1e-6dd28394cacc", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"expected_return_datetime\": \"2025-07-10T15:30:00Z\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/a18e1703-e2f2-4818-9499-9959060dacdc/return-time", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "a18e1703-e2f2-4818-9499-9959060dacdc", "return-time"]}}, "response": []}, {"name": "Start Odometer", "id": "22710-70951f85-51d4-4c69-9ad7-c8d1b475764f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"start_odometer_reading\": 50000\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/ab30b828-5e41-4b34-b11d-a82e790bdb41/odometer", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "ab30b828-5e41-4b34-b11d-a82e790bdb41", "odometer"]}}, "response": []}, {"name": "End Odometer", "id": "22710-673f7b69-0793-4ec2-a441-a39b8dcfcf3a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"end_odometer_reading\": 50000\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/1f57f644-12b5-4c30-9ceb-9a7ce8ad387f/odometer", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "1f57f644-12b5-4c30-9ceb-9a7ce8ad387f", "odometer"]}}, "response": []}, {"name": "Reassign <PERSON>erson", "id": "22710-d3e07a2d-2a1b-4002-ac7a-bf4662793233", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"sales_person_uuid\": \"8861bcb5-f708-4ac1-a7fe-afda8d02eddb\",\n    \"sales_person_accompanying_uuid\": \"8cc81022-93f7-40a6-9bda-106e4dfec79d\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/1f57f644-12b5-4c30-9ceb-9a7ce8ad387f/reassign", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "1f57f644-12b5-4c30-9ceb-9a7ce8ad387f", "reassign"]}}, "response": []}, {"name": "CreateDrive", "id": "22710-7cb0a431-f8ee-4629-92df-bad3d98453f3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"vehicle_uuid\": \"38de96f4-ad53-4251-addf-8e74b6cece3f\",\n    \"drive_type\": \"test_drive\",\n    \"sales_person_uuid\": \"8861bcb5-f708-4ac1-a7fe-afda8d02eddb\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives"]}}, "response": []}, {"name": "Activities", "id": "22710-7e327bdd-ebde-40e5-b68f-6aa2fdccfc80", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives?start_date_from=2025-06-01&start_date_to=2025-07-07&vehicle_uuid=bfad23a6-**************-5830bda3ff91&sales_person_uuid=8861bcb5-f708-4ac1-a7fe-afda8d02eddb&sold_status=sold&overdue=true&drive_type=enquiry&customer_uuid=ffde228a-f91e-4a47-ab3d-022baa286444&status=completed&trade_plate_uuid=8861bcb5-f708-4ac1-a7fe-afda8d02eddb&updated_from=2025-06-01&updated_to=2025-07-07", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives"], "query": [{"key": "start_date_from", "value": "2025-06-01"}, {"key": "start_date_to", "value": "2025-07-07"}, {"key": "vehicle_uuid", "value": "bfad23a6-**************-5830bda3ff91"}, {"key": "sales_person_uuid", "value": "8861bcb5-f708-4ac1-a7fe-afda8d02eddb"}, {"key": "sold_status", "value": "sold"}, {"key": "overdue", "value": "true", "type": "text"}, {"key": "drive_type", "value": "enquiry"}, {"key": "customer_uuid", "value": "ffde228a-f91e-4a47-ab3d-022baa286444"}, {"key": "status", "value": "completed"}, {"key": "trade_plate_uuid", "value": "8861bcb5-f708-4ac1-a7fe-afda8d02eddb"}, {"key": "updated_from", "value": "2025-06-01", "type": "text"}, {"key": "updated_to", "value": "2025-07-07", "type": "text"}]}}, "response": []}, {"name": "CreateDamageReport", "id": "22710-dd5010ff-4895-41d3-8511-2d281374882f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "damage_type", "value": "initial"}, {"type": "text", "key": "damage_notes", "value": "Minor scratches on the front bumper"}, {"type": "file", "key": "media_files[]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/large_video.mp4"}, {"type": "file", "key": "media_files[]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/car_1.png"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/4a2bc06a-02b4-47ff-9b4d-5b4457fd247e/damage-report", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "4a2bc06a-02b4-47ff-9b4d-5b4457fd247e", "damage-report"]}}, "response": []}, {"name": "Assign Tradeplate", "id": "22710-c51f8537-6d5a-4987-a506-e8c43cd7bf1c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"trade_plate_uuid\": \"1e923c6f-a497-43fb-b437-4654afd61bec\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/7d5caf38-a2ea-488e-bce2-ae81c907af24/trade-plate", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "7d5caf38-a2ea-488e-bce2-ae81c907af24", "trade-plate"]}}, "response": []}, {"name": "UpdateDriveCustomerExisting", "id": "22710-e23cc0dc-5757-493a-83a3-2b7411fcc263", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n   \"customer_uuid\": \"80a2e7b5-2eea-4160-9a03-28ecfb4afc19\"\n\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/9eb414ca-b44b-4617-baeb-c93853bd989d/customer", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "9eb414ca-b44b-4617-baeb-c93853bd989d", "customer"]}}, "response": []}, {"name": "UpdateDriveCustomerNew", "id": "22710-9a817ab6-4605-4248-a3a6-d6b1df4276c4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "customer_info[first_name]", "value": "<PERSON>"}, {"type": "text", "key": "customer_info[last_name]", "value": "<PERSON><PERSON>"}, {"type": "text", "key": "customer_info[email]", "value": "<EMAIL>"}, {"type": "text", "key": "customer_info[phone_number]", "value": "+61412345678"}, {"type": "text", "key": "customer_info[driver_license][licence_number]", "value": "**********"}, {"type": "text", "key": "customer_info[driver_license][expiry_date]", "value": "2025-12-31"}, {"type": "file", "key": "customer_info[driver_license][front_image]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/back_dl.png"}, {"type": "file", "key": "customer_info[driver_license][back_image]", "src": "/Users/<USER>/apps/dealer-drive-backend/spec/fixtures/files/front_dl.png"}, {"key": "customer_info[age]", "value": "34", "type": "text", "uuid": "9207164c-45b7-4ffd-8123-01023ad87bf6"}, {"key": "customer_info[gender]", "value": "male", "type": "text", "uuid": "5e6ec691-79a7-42d1-a80f-c82beb8f6a49"}, {"key": "customer_info[address_line1]", "value": "Address1", "type": "text", "uuid": "0388e973-1c7f-487e-b1fe-c0e92e8444f6"}, {"key": "customer_info[address_line2]", "value": "Address1", "type": "text", "uuid": "f20fde14-5135-489c-871e-e31accf7279a"}, {"key": "customer_info[suburb]", "value": "Suburb", "type": "text", "uuid": "24d75146-98fa-4447-a04c-f56d7bac1bcb"}, {"key": "customer_info[city]", "value": "Sydney", "type": "text", "uuid": "d2c424e9-e8c3-4b15-8eb7-5cb63640ad43"}, {"key": "customer_info[state]", "value": "NSW", "type": "text", "uuid": "000260a0-c8ca-4fa5-b2be-9e72d7f62c4d"}, {"key": "customer_info[country]", "value": "Australia", "type": "text", "uuid": "32d7156e-4fdb-4ee6-94cc-dc0ce64ae41b"}, {"key": "customer_info[postcode]", "value": "4215", "type": "text", "uuid": "92697aca-def7-45b2-baab-8fd19b50c91a"}, {"key": "customer_info[company_name]", "value": "Buckridge Inc", "type": "text", "uuid": "762d1a0c-987f-4bed-b38f-e3759f458d84"}, {"key": "customer_info[driver_license][issuing_state]", "value": "NSW", "type": "text", "uuid": "58a1b607-9605-45b7-b79c-9b87a06f601a"}, {"key": "customer_info[driver_license][issuing_country]", "value": "Australia", "type": "text", "uuid": "adf2d759-43b8-4701-a0e5-ef6b79895c4e"}, {"key": "customer_info[driver_license][full_name]", "value": "Tester <PERSON>", "type": "text", "uuid": "2ec35119-43b8-4e48-9680-2a6205ec4960"}, {"key": "customer_info[driver_license][date_of_birth]", "value": "1983-02-11", "type": "text", "uuid": "513f54ef-9ef0-4e2e-b414-763bd5459e15"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/9eb414ca-b44b-4617-baeb-c93853bd989d/customer", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "9eb414ca-b44b-4617-baeb-c93853bd989d", "customer"]}}, "response": []}, {"name": "brands", "id": "22710-d1d177ec-6196-4dff-bb55-6d69249b4e22", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/brands", "host": ["{{host}}"], "path": ["api", "v1", "brands"]}}, "response": []}, {"name": "createVehicleWithPhotos", "id": "22710-4e7a9997-472e-4233-a528-768a4a4bed2f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}, {"key": "Content-Type", "value": "multipart/form-data", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"type": "text", "key": "vehicle[vin]", "value": "1HGBH41JXMN109186"}, {"type": "text", "key": "vehicle[stock_number]", "value": "TOY001"}, {"type": "text", "key": "vehicle[rego]", "value": "ABC123"}, {"type": "text", "key": "vehicle[make]", "value": "Toyota"}, {"type": "text", "key": "vehicle[model]", "value": "Cam<PERSON>"}, {"type": "text", "key": "vehicle[build_year]", "value": "2024"}, {"type": "text", "key": "vehicle[color]", "value": "Blue"}, {"type": "text", "key": "vehicle[vehicle_type]", "value": "new_vehicle"}, {"type": "text", "key": "vehicle[status]", "value": "available"}, {"type": "text", "key": "vehicle[last_known_odometer_km]", "value": "10000"}, {"type": "text", "key": "vehicle[last_known_fuel_gauge_level]", "value": "50"}, {"type": "text", "key": "vehicle[available_for_drive]", "value": "true"}, {"type": "text", "key": "vehicle[is_trade_plate_used]", "value": "false"}, {"type": "text", "key": "vehicle[rego_expiry]", "value": "2025-12-31"}, {"type": "file", "key": "vehicle[photos][]", "src": "/Users/<USER>/Library/CloudStorage/Dropbox/Downloads/archive/images.jpeg"}, {"type": "file", "key": "vehicle[photos][]", "src": "/Users/<USER>/Library/CloudStorage/Dropbox/Downloads/archive/independence.png"}, {"key": "vehicle[brand_uuid]", "value": "7266a3d0-e278-41a7-934e-29b79832c3d4", "type": "text", "uuid": "3ec1032b-853d-4f6c-ae7d-8e5057a157f7"}]}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/vehicles", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "vehicles"]}}, "response": []}, {"name": "createVehicleWOPhoto", "id": "22710-28b8ee37-2ea2-42eb-9bf0-bd57d8fde21a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"make\": \"Toyota\",\n    \"model\": \"Camry\",\n    \"build_year\": 2023,\n    \"color\": \"Blue\",\n    \"vehicle_type\": \"new_vehicle\",\n    \"vin\": \"1HGBH41JXMN109186\",\n    \"stock_number\": \"TOY001\",\n    \"rego\": \"ABC123\",\n    \"status\": \"available\",\n    \"last_known_odometer_km\": 10000,\n    \"last_known_fuel_gauge_level\": 50,\n    \"available_for_drive\": true,\n    \"is_trade_plate_used\": false,\n    \"rego_expiry\": \"2026-12-31\",\n    \"brand_uuid\": \"7266a3d0-e278-41a7-934e-29b79832c3d4\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/vehicles", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "vehicles"]}}, "response": []}, {"name": "GetDrives", "id": "22710-5ec737e6-547a-441a-9bf3-4d38c26ff5a2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives?eligible_for_return=true", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives"], "query": [{"key": "vehicle_uuid", "value": "617c748e-299d-4f25-bb90-6935ccaa512a", "disabled": true}, {"key": "customer_uuid", "value": "ffde228a-f91e-4a47-ab3d-022baa286444", "disabled": true}, {"key": "drive_type", "value": "test_drive", "disabled": true}, {"key": "status", "value": "completed", "disabled": true}, {"key": "sales_person_uuid", "value": "8861bcb5-f708-4ac1-a7fe-afda8d02eddb", "disabled": true}, {"key": "sold_status", "value": "sold", "disabled": true}, {"key": "trade_plate_uuid", "value": "8861bcb5-f708-4ac1-a7fe-afda8d02eddb", "disabled": true}, {"key": "start_date_from", "value": "2025-05-01", "disabled": true}, {"key": "start_date_to", "value": "2025-08-01", "disabled": true}, {"key": "eligible_for_return", "value": "true", "type": "text"}]}}, "response": []}, {"name": "CreateEnquiry", "id": "22710-ed20f869-3886-4591-b82d-1e3370f6225d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n   \"enquiry\": {\n      \"vehicle_uuid\": \"6bb48864-77d3-45c5-9d55-f82b2da19629\",\n      \"customer_uuid\": \"6a9f0ba3-41a6-44db-a9b2-0dd3597d7436\",\n    //   \"sales_person_uuid\": \"b5e7cf46-02e0-4737-824c-8fab66688acb\",\n      \"notes\": \"Customer enquiry about vehicle features\"\n   }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/enquiries", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "enquiries"]}}, "response": []}, {"name": "Drives Dashboard", "id": "22710-c42a5e2d-fd04-46fd-a577-6335779173d8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/dashboard", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "dashboard"]}}, "response": []}, {"name": "Complete Drive", "id": "22710-125cbb01-a34c-489c-b0d2-cf6c34c7548d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "body": {"mode": "raw", "raw": "{\n    \"notes\": \"enjoyed2\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/drives/048f1841-ea1b-4867-a779-d7c30d039e87/complete", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "drives", "048f1841-ea1b-4867-a779-d7c30d039e87", "complete"]}}, "response": []}, {"name": "vehicles", "id": "22710-88c9eac8-57a2-4e45-b0ff-2f7495e530e4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access-token}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Device-ID", "value": "{{device_id}}"}], "url": {"raw": "{{host}}/api/v1/dealerships/9d7267a7-108b-4e62-9d6d-77cd3d86d787/vehicles?per_page=50&page=1&vehicle_type=new_vehicle&query=camry", "host": ["{{host}}"], "path": ["api", "v1", "dealerships", "9d7267a7-108b-4e62-9d6d-77cd3d86d787", "vehicles"], "query": [{"key": "per_page", "value": "50"}, {"key": "page", "value": "1"}, {"key": "vehicle_type", "value": "new_vehicle"}, {"key": "query", "value": "camry"}]}}, "response": []}], "event": [{"listen": "prerequest", "script": {"id": "74f00bd6-83e8-4266-995d-57483d3835c2", "type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"id": "8d0d261a-c462-45f5-9734-917764d3873e", "type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "device_id", "value": "DeviceID", "type": "default"}, {"key": "app_version", "value": "11", "type": "default"}, {"key": "build", "value": "1212", "type": "default"}, {"key": "os", "value": "ios", "type": "default"}, {"key": "access-token", "value": ""}, {"key": "refresh-token", "value": ""}, {"key": "temp-token", "value": ""}, {"key": "host", "value": "https://morning-escarpment-47088-1580637df638.herokuapp.com", "type": "string"}]}